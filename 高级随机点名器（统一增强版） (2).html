<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>高级随机点名器 · 统一增强版</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <style>
    :root{--bg1:#667eea;--bg2:#764ba2}
    html,body{height:100%}
    body{font-family:'Inter',sans-serif;min-height:100vh;background:linear-gradient(135deg,var(--bg1) 0%,var(--bg2) 100%)}
    .glass{background:rgba(255,255,255,.12);backdrop-filter:blur(10px);border-radius:20px;border:1px solid rgba(255,255,255,.22)}
    .btn{border-radius:9999px;padding:.65rem 1.25rem;font-weight:600;transition:.2s}
    .btn-primary{background:linear-gradient(135deg,var(--bg1),var(--bg2));color:#fff}
    .btn-ghost{background:rgba(255,255,255,.18);color:#fff;border:1px solid rgba(255,255,255,.3)}
    .pill{background:rgba(255,255,255,.18);border:1px solid rgba(255,255,255,.3)}
    .modal{position:fixed;inset:0;background:rgba(0,0,0,.55);display:none;align-items:center;justify-content:center;z-index:50}
    .modal.show{display:flex}
    .modal-card{max-width:95vw;max-height:85vh;overflow:auto}
    .ticker{animation:ticker 18s linear infinite}
    @keyframes ticker{0%{transform:translateY(0)}100%{transform:translateY(-100%)}}
    .fade-in{animation:fade .5s ease-out}@keyframes fade{from{opacity:0;transform:scale(.96)}to{opacity:1;transform:scale(1)}}
  </style>
</head>
<body class="text-white">
  <div class="container mx-auto px-4 py-8 max-w-7xl">
    <header class="text-center mb-8">
      <h1 class="text-4xl md:text-5xl font-extrabold drop-shadow-sm">高级随机点名器 · 统一增强版</h1>
      <p class="text-white/80 mt-2">支持批量导入、多列识别、公平算法与高级功能。</p>
    </header>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <section class="lg:col-span-2 space-y-6">
        <div class="glass p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold">📁 名单导入</h2>
            <div class="flex gap-2">
              <button id="btnCustomTemplate" class="btn btn-ghost text-sm">自定义模板</button>
              <button id="btnClearAll" class="btn btn-ghost text-sm">清空名单</button>
            </div>
          </div>
          <div class="flex flex-wrap gap-3">
            <input id="fileInput" class="hidden" type="file" accept=".txt,.csv,.xlsx,.xls" multiple>
            <button class="btn btn-ghost" onclick="document.getElementById('fileInput').click()">选择文件</button>
            <button id="btnPaste" class="btn btn-ghost">📋 粘贴导入</button>
            <label class="flex items-center gap-2 text-sm text-white/90 pill px-3 py-2 rounded-full">
              <input id="hasHeader" type="checkbox" class="scale-110" checked>
              <span>首行为表头</span>
            </label>
          </div>
          <p id="importStatus" class="text-white/80 text-sm mt-3"></p>
        </div>

        <div class="glass p-6">
          <h2 class="text-xl font-semibold mb-4">✏️ 快速添加</h2>
          <div class="flex gap-3">
            <input id="nameInput" type="text" placeholder="输入姓名（支持批量，张三|一班）" class="flex-1 px-4 py-3 rounded-full bg-white/10 text-white border border-white/20 focus:outline-none">
            <button id="btnAdd" class="btn btn-primary">添加</button>
          </div>
        </div>

        <div class="glass p-8">
          <div id="display" class="min-h-[180px] flex items-center justify-center text-center">
            <p class="text-white/70">点击下方按钮开始点名…</p>
          </div>
          <div class="mt-6 flex flex-wrap justify-center gap-3">
            <button id="btnDraw" class="btn btn-primary text-lg px-8 py-4">🎯 开始点名</button>
            <button id="btnMulti" class="btn btn-ghost">批量抽取</button>
            <button id="btnFullscreen" class="btn btn-ghost">全屏显示</button>
            <button id="btnAutoCycle" class="btn btn-ghost">循环点名</button>
            <button id="btnAdvanced" class="btn btn-ghost">高级设置</button>
          </div>
        </div>
      </section>

      <aside class="space-y-6">
        <div class="glass p-6">
          <div class="flex items-center justify-between mb-3">
            <h2 class="text-xl font-semibold">👥 名单管理</h2>
            <span class="pill px-3 py-1 rounded-full text-sm">共 <b id="countAll">0</b> 人</span>
          </div>
          <div id="list" class="max-h-[320px] overflow-y-auto space-y-2 pr-1">
            <p class="text-white/60 text-center py-4">暂无数据</p>
          </div>
        </div>
      </aside>
    </div>
  </div>

  <div id="modalTable" class="modal">
    <div class="modal-card glass p-6 rounded-2xl w-[min(900px,95vw)] text-white">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-2xl font-bold">选择包含姓名的列</h3>
        <button class="btn btn-ghost" onclick="closeModal('modalTable')">关闭</button>
      </div>
      <div id="tablePreview" class="bg-white/10 rounded-xl overflow-auto max-h-[50vh]"></div>
      <div class="mt-4 flex justify-end gap-2">
        <button class="btn btn-ghost" onclick="closeModal('modalTable')">取消</button>
        <button id="btnConfirmColumns" class="btn btn-primary">确认导入</button>
      </div>
    </div>
  </div>

  <div id="modalTemplate" class="modal">
    <div class="modal-card glass p-6 rounded-2xl w-[min(700px,95vw)] text-white">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-2xl font-bold">自定义模板设置</h3>
        <button class="btn btn-ghost" onclick="closeModal('modalTemplate')">关闭</button>
      </div>
      <div class="space-y-4">
        <div>
          <label class="block mb-1">姓名列标题：</label>
          <input id="templateName" type="text" class="w-full px-3 py-2 rounded bg-white/10 border border-white/20">
        </div>
        <div>
          <label class="block mb-1">标签列标题（可选）：</label>
          <input id="templateTag" type="text" class="w-full px-3 py-2 rounded bg-white/10 border border-white/20">
        </div>
        <div id="templatePreview" class="bg-white/10 rounded-xl p-3 text-sm text-white/80"></div>
      </div>
      <div class="mt-4 flex justify-end gap-2">
        <button class="btn btn-ghost" onclick="closeModal('modalTemplate')">取消</button>
        <button id="btnSaveTemplate" class="btn btn-primary">保存模板</button>
      </div>
    </div>
  </div>

  <div id="modalAdvanced" class="modal">
    <div class="modal-card glass p-6 rounded-2xl w-[min(800px,95vw)] text-white">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-2xl font-bold">高级设置</h3>
        <button class="btn btn-ghost" onclick="closeModal('modalAdvanced')">关闭</button>
      </div>
      <div class="space-y-4">
        <label class="flex items-center gap-2"><input type="checkbox" id="optNoRepeat"> 不重复抽取</label>
        <label class="flex items-center gap-2"><input type="checkbox" id="optFair"> 公平模式（已抽中过的权重降低）</label>
        <label class="flex items-center gap-2"><input type="checkbox" id="optSound"> 播放提示音</label>
        <label class="flex items-center gap-2"><input type="checkbox" id="optFireworks"> 抽中显示烟花</label>
        <div>
          <label>随机种子（可复现实验）：</label>
          <input id="seedInput" type="text" class="w-full px-3 py-2 rounded bg-white/10 border border-white/20" placeholder="留空则为真随机">
        </div>
        <div>
          <label>循环间隔（秒）：</label>
          <input id="cycleInterval" type="number" class="w-full px-3 py-2 rounded bg-white/10 border border-white/20" value="5">
        </div>
      </div>
      <div class="mt-4 flex justify-end gap-2">
        <button class="btn btn-ghost" onclick="closeModal('modalAdvanced')">取消</button>
        <button class="btn btn-primary" onclick="saveAdvanced()">保存设置</button>
      </div>
    </div>
  </div>

  <div id="modalPaste" class="modal">
    <div class="modal-card glass p-6 rounded-2xl w-[min(700px,95vw)] text-white">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-2xl font-bold">粘贴导入名单</h3>
        <button class="btn btn-ghost" onclick="closeModal('modalPaste')">关闭</button>
      </div>
      <textarea id="pasteArea" class="w-full h-[45vh] p-3 rounded-xl bg-white/10 border border-white/20" placeholder="每行一个姓名；可写标签：张三|一班"></textarea>
      <div class="mt-4 flex justify-end gap-2">
        <button class="btn btn-ghost" onclick="closeModal('modalPaste')">取消</button>
        <button id="btnPasteImport" class="btn btn-primary">导入</button>
      </div>
    </div>
  </div>

  <script>
    function showTableSelector(data){
      if(!data || !data.length) return;
      const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);
      const cols = headers.map((h,i)=>({index:i,label: (h||'').toString()}));
      const filtered = cols.filter(c=>isNaN(Number(c.label.replace(/[^0-9]/g,''))));
      const table = document.createElement('table');
      table.className = 'w-full text-sm';
      const thead = document.createElement('thead');
      const trh = document.createElement('tr');
      filtered.forEach(c=>{
        const th = document.createElement('th');
        th.textContent = c.label;
        trh.appendChild(th);
      });
      thead.appendChild(trh);
      table.appendChild(thead);
      const tbody = document.createElement('tbody');
      (data.slice(0,20)).forEach(r=>{
        const tr = document.createElement('tr');
        filtered.forEach(c=>{
          const td = document.createElement('td');
          td.textContent = Array.isArray(r)? r[c.index] : r[c.label];
          tr.appendChild(td);
        });
        tbody.appendChild(tr);
      });
      table.appendChild(tbody);
      const wrap=document.getElementById('tablePreview');
      wrap.innerHTML='';wrap.appendChild(table);
      document.getElementById('modalTable').classList.add('show');
    }

    function closeModal(id){document.getElementById(id).classList.remove('show');}

    document.getElementById('btnCustomTemplate').addEventListener('click',()=>{
      document.getElementById('modalTemplate').classList.add('show');
    });

    document.getElementById('btnSaveTemplate').addEventListener('click',()=>{
      const name=document.getElementById('templateName').value.trim();
      const tag=document.getElementById('templateTag').value.trim();
      if(!name){alert('请填写姓名列标题');return;}
      localStorage.setItem('customTemplate',JSON.stringify({name,tag}));
      // 预览模板效果
      document.getElementById('templatePreview').innerHTML =
        `<p>预览：将使用 <b>${name}</b>${tag? ' 作为标签列 '+tag:''}</p>`;
      alert('自定义模板已保存！');
      closeModal('modalTemplate');
    });

    document.getElementById('btnAdvanced').addEventListener('click',()=>{
      document.getElementById('modalAdvanced').classList.add('show');
    });

    function saveAdvanced(){
      const settings={
        noRepeat:document.getElementById('optNoRepeat').checked,
        fair:document.getElementById('optFair').checked,
        sound:document.getElementById('optSound').checked,
        fireworks:document.getElementById('optFireworks').checked,
        seed:document.getElementById('seedInput').value,
        interval:parseInt(document.getElementById('cycleInterval').value)||5
      };
      localStorage.setItem('advancedSettings',JSON.stringify(settings));
      alert('高级设置已保存！');
      closeModal('modalAdvanced');
    }
  </script>
</body>
</html>
