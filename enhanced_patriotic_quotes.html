<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>家国情怀名言警句集锦</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e90ff',
                        secondary: '#ffd700',
                        accent: '#ff4757',
                        success: '#2ed573',
                        dark: '#0d1a26',
                    },
                    fontFamily: {
                        'noto': ['"Noto Sans SC"', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    
    <style type="text/tailwindcss">
        @layer utilities {
            .text-shadow {
                text-shadow: 0 2px 10px rgba(0, 0, 0, 0.6);
            }
            .bg-blur {
                backdrop-filter: blur(8px);
            }
            .card-hover {
                @apply transition-all duration-300 hover:-translate-y-2 hover:shadow-lg;
            }
            .content-auto {
                content-visibility: auto;
            }
        }
    </style>
    
    <style>
        /* 基础动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
            opacity: 0;
        }
        
        /* 脉冲动画 */
        @keyframes pulse {
            0% { transform: scale(0.8); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(0.8); opacity: 1; }
        }
        
        .animate-pulse {
            animation: pulse 1.5s infinite;
        }
        
        /* 音频波形动画 */
        @keyframes audioWave {
            0%, 100% { transform: scaleY(0.3); }
            50% { transform: scaleY(1); }
        }
        
        .wave-bar {
            display: inline-block;
            width: 3px;
            margin: 0 1px;
            background-color: #2ed573;
            border-radius: 3px;
            animation: audioWave 1s ease-in-out infinite;
        }
        
        .wave-bar:nth-child(2) { animation-delay: 0.1s; }
        .wave-bar:nth-child(3) { animation-delay: 0.2s; }
        .wave-bar:nth-child(4) { animation-delay: 0.3s; }
        .wave-bar:nth-child(5) { animation-delay: 0.4s; }
    </style>
</head>
<body class="font-noto bg-dark text-white overflow-hidden">
    <div class="relative w-full h-screen max-w-[1920px] mx-auto">
        <!-- 背景图片与遮罩 -->
        <div class="absolute inset-0 z-0">
            <img src="https://picsum.photos/id/1054/1920/1080" alt="古色古香的中国风背景，有卷轴和毛笔元素" class="w-full h-full object-cover opacity-40">
            <div class="absolute inset-0 bg-gradient-to-b from-dark/70 via-dark/50 to-dark"></div>
        </div>
        
        <!-- 装饰性光效 -->
        <div class="absolute top-1/4 left-1/4 w-[600px] h-[600px] bg-primary/10 rounded-full blur-3xl -z-10"></div>
        <div class="absolute bottom-1/4 right-1/4 w-[500px] h-[500px] bg-secondary/10 rounded-full blur-3xl -z-10"></div>
        
        <!-- 标题区域 -->
        <header class="pt-12 pb-6 text-center relative z-10 animate-fade-in">
            <h1 class="text-[clamp(2.5rem,5vw,4rem)] font-bold bg-gradient-to-r from-white to-secondary bg-clip-text text-transparent text-shadow mb-4">
                家国情怀名言警句集锦
            </h1>
            <div class="w-32 h-1 bg-accent mx-auto rounded-full"></div>
            <p class="mt-6 text-gray-300 max-w-2xl mx-auto text-[clamp(1rem,2vw,1.25rem)]">
                这些穿越时空的智慧箴言，承载着中华民族最深沉的家国情怀
            </p>
        </header>
        
        <!-- 名言内容区域 -->
        <main class="container mx-auto px-4 py-8 relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
                <!-- 名言1 -->
                <div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="苟利国家，不求富贵">
                    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="flex items-start gap-4 relative">
                        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
                        <div class="flex-1">
                            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                                苟利国家，不求富贵
                            </p>
                            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——《礼记》</p>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
                            <i class="fa fa-play"></i>
                            <span>播放朗读</span>
                        </button>
                        <div class="audio-indicator hidden">
                            <span class="wave-bar h-2"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-4"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-2"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 名言2 -->
                <div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="位卑未敢忘忧国">
                    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="flex items-start gap-4 relative">
                        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
                        <div class="flex-1">
                            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                                位卑未敢忘忧国
                            </p>
                            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——陆游</p>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
                            <i class="fa fa-play"></i>
                            <span>播放朗读</span>
                        </button>
                        <div class="audio-indicator hidden">
                            <span class="wave-bar h-2"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-4"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-2"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 名言3 -->
                <div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="人生自古谁无死，留取丹心照汗青">
                    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="flex items-start gap-4 relative">
                        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
                        <div class="flex-1">
                            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                                人生自古谁无死，留取丹心照汗青
                            </p>
                            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——文天祥</p>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
                            <i class="fa fa-play"></i>
                            <span>播放朗读</span>
                        </button>
                        <div class="audio-indicator hidden">
                            <span class="wave-bar h-2"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-4"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-2"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 名言4 -->
                <div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="先天下之忧而忧，后天下之乐而乐">
                    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="flex items-start gap-4 relative">
                        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
                        <div class="flex-1">
                            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                                先天下之忧而忧，后天下之乐而乐
                            </p>
                            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——范仲淹</p>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
                            <i class="fa fa-play"></i>
                            <span>播放朗读</span>
                        </button>
                        <div class="audio-indicator hidden">
                            <span class="wave-bar h-2"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-4"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-2"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 名言5 -->
                <div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="天下兴亡，匹夫有责">
                    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="flex items-start gap-4 relative">
                        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
                        <div class="flex-1">
                            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                                天下兴亡，匹夫有责
                            </p>
                            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——顾炎武</p>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
                            <i class="fa fa-play"></i>
                            <span>播放朗读</span>
                        </button>
                        <div class="audio-indicator hidden">
                            <span class="wave-bar h-2"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-4"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-2"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 名言6 -->
                <div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="我以我血荐轩辕">
                    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="flex items-start gap-4 relative">
                        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
                        <div class="flex-1">
                            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                                我以我血荐轩辕
                            </p>
                            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——鲁迅</p>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
                            <i class="fa fa-play"></i>
                            <span>播放朗读</span>
                        </button>
                        <div class="audio-indicator hidden">
                            <span class="wave-bar h-2"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-4"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-2"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 名言7 -->
                <div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="为什么我的眼里常含泪水？因为我对这土地爱得深沉">
                    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="flex items-start gap-4 relative">
                        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
                        <div class="flex-1">
                            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                                为什么我的眼里常含泪水？因为我对这土地爱得深沉
                            </p>
                            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——艾青</p>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
                            <i class="fa fa-play"></i>
                            <span>播放朗读</span>
                        </button>
                        <div class="audio-indicator hidden">
                            <span class="wave-bar h-2"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-4"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-2"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 名言8 -->
                <div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group md:col-span-2 lg:col-span-1" data-text="清澈的爱，只为中国">
                    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="flex items-start gap-4 relative">
                        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
                        <div class="flex-1">
                            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                                清澈的爱，只为中国
                            </p>
                            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——陈祥榕</p>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
                            <i class="fa fa-play"></i>
                            <span>播放朗读</span>
                        </button>
                        <div class="audio-indicator hidden">
                            <span class="wave-bar h-2"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-4"></span>
                            <span class="wave-bar h-3"></span>
                            <span class="wave-bar h-2"></span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- 底部装饰 -->
        <footer class="absolute bottom-0 left-0 w-full h-1.5 bg-gradient-to-r from-success via-primary to-accent z-10"></footer>
        
        <!-- 页脚信息 -->
        <div class="absolute bottom-4 left-0 right-0 text-center text-gray-500 text-sm z-10">
            家国情怀 · 传承不息
        </div>
    </div>

    <script>
        // 音频播放管理
        class AudioPlayer {
            constructor() {
                this.speech = window.speechSynthesis;
                this.currentUtterance = null;
                this.playingCard = null;
            }
            
            // 播放文本
            playText(text, cardElement) {
                // 停止当前播放
                this.stop();
                
                // 创建新的语音实例
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'zh-CN';
                utterance.rate = 0.9; // 语速稍慢，更适合朗读名言
                utterance.pitch = 1.1; // 音调略高，更显庄重
                utterance.volume = 1;
                
                // 保存当前播放实例
                this.currentUtterance = utterance;
                this.playingCard = cardElement;
                
                // 更新UI显示播放状态
                const playBtn = cardElement.querySelector('.play-btn');
                const audioIndicator = cardElement.querySelector('.audio-indicator');
                
                playBtn.innerHTML = '<i class="fa fa-pause"></i><span>暂停朗读</span>';
                audioIndicator.classList.remove('hidden');
                
                // 播放结束时重置UI
                utterance.onend = () => {
                    this.resetUI();
                };
                
                // 开始播放
                this.speech.speak(utterance);
            }
            
            // 停止播放
            stop() {
                if (this.currentUtterance) {
                    this.speech.cancel();
                    this.resetUI();
                }
            }
            
            // 重置UI状态
            resetUI() {
                if (this.playingCard) {
                    const playBtn = this.playingCard.querySelector('.play-btn');
                    const audioIndicator = this.playingCard.querySelector('.audio-indicator');
                    
                    playBtn.innerHTML = '<i class="fa fa-play"></i><span>播放朗读</span>';
                    audioIndicator.classList.add('hidden');
                }
                
                this.currentUtterance = null;
                this.playingCard = null;
            }
        }
        
        // 初始化音频播放器
        const audioPlayer = new AudioPlayer();
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 为名言卡片添加延迟动画
            const quoteCards = document.querySelectorAll('.quote-card');
            quoteCards.forEach((card, index) => {
                card.style.animation = `fadeIn 0.6s ease-out forwards ${0.1 + index * 0.1}s`;
                card.style.opacity = '0';
            });
            
            // 为播放按钮添加点击事件
            document.querySelectorAll('.play-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const card = btn.closest('.quote-card');
                    const text = card.dataset.text;
                    
                    // 如果是当前播放的卡片，则停止播放
                    if (audioPlayer.playingCard === card) {
                        audioPlayer.stop();
                    } else {
                        // 否则播放新的文本
                        audioPlayer.playText(text, card);
                    }
                });
            });
            
            // 点击卡片也可以播放音频
            document.querySelectorAll('.quote-card').forEach(card => {
                card.addEventListener('click', () => {
                    const text = card.dataset.text;
                    
                    // 如果是当前播放的卡片，则停止播放
                    if (audioPlayer.playingCard === card) {
                        audioPlayer.stop();
                    } else {
                        // 否则播放新的文本
                        audioPlayer.playText(text, card);
                    }
                });
            });
        });
    </script>
</body>
</html>
    