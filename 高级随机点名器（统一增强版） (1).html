<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>高级随机点名器 · 统一增强版</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- PapaParse 用于解析 CSV 文本 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
  <!-- 注意：XLSX（SheetJS）动态加载（见脚本）以避免在某些环境中出现 XLSX 未定义的问题） -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <style>
    :root{--bg1:#667eea;--bg2:#764ba2}
    html,body{height:100%}
    body{font-family:'Inter',-apple-system,BlinkMacSystemFont,"Segoe UI",<PERSON><PERSON>,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans",sans-serif;min-height:100vh;background:linear-gradient(135deg,var(--bg1) 0%,var(--bg2) 100%)}
    .glass{background:rgba(255,255,255,.12);backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border-radius:20px;border:1px solid rgba(255,255,255,.22);box-shadow:0 10px 30px rgba(0,0,0,.15)}
    .btn{border-radius:9999px;padding:.65rem 1.25rem;font-weight:600;transition:.2s}
    .btn-primary{background:linear-gradient(135deg,var(--bg1),var(--bg2));color:#fff}
    .btn-primary:hover{transform:translateY(-2px);box-shadow:0 10px 25px rgba(102,126,234,.4)}
    .btn-ghost{background:rgba(255,255,255,.18);color:#fff;border:1px solid rgba(255,255,255,.3)}
    .btn-ghost:hover{background:rgba(255,255,255,.28)}
    .pill{background:rgba(255,255,255,.18);border:1px solid rgba(255,255,255,.3)}
    .name-item{background:rgba(255,255,255,.12)}
    .modal{position:fixed;inset:0;background:rgba(0,0,0,.55);display:none;align-items:center;justify-content:center;z-index:50}
    .modal.show{display:flex}
    .modal-card{max-width:95vw;max-height:85vh;overflow:auto}
    .ticker{animation:ticker 18s linear infinite}
    @keyframes ticker{0%{transform:translateY(0)}100%{transform:translateY(-100%)}}
    .fade-in{animation:fade .5s ease-out}@keyframes fade{from{opacity:0;transform:scale(.96)}to{opacity:1;transform:scale(1)}}
  </style>
</head>
<body class="text-white">
  <div class="container mx-auto px-4 py-8 max-w-7xl">
    <header class="text-center mb-8">
      <h1 class="text-4xl md:text-5xl font-extrabold drop-shadow-sm">高级随机点名器 · 统一增强版</h1>
      <p class="text-white/80 mt-2">合并「随机点名」与「优化版」，全面修复与增强，支持批量导入、多列识别、公平算法与高级功能。</p>
    </header>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左：控制区 -->
      <section class="lg:col-span-2 space-y-6">
        <!-- 导入区 -->
        <div class="glass p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold">📁 名单导入</h2>
            <div class="flex gap-2">
              <button id="btnTemplate" class="btn btn-ghost text-sm">下载模板</button>
              <button id="btnClearAll" class="btn btn-ghost text-sm">清空名单</button>
            </div>
          </div>
          <div class="flex flex-wrap gap-3">
            <input id="fileInput" class="hidden" type="file" accept=".txt,.csv,.xlsx,.xls" multiple>
            <button class="btn btn-ghost" onclick="document.getElementById('fileInput').click()">选择文件</button>
            <button id="btnPaste" class="btn btn-ghost">📋 粘贴导入</button>
            <label class="flex items-center gap-2 text-sm text-white/90 pill px-3 py-2 rounded-full">
              <input id="hasHeader" type="checkbox" class="scale-110" checked>
              <span>首行为表头</span>
            </label>
            <button id="btnTestPreview" class="btn btn-ghost text-sm">测试列选择</button>
            <button id="btnTestXLSX" class="btn btn-ghost text-sm">测试加载 XLSX</button>
          </div>
          <p id="importStatus" class="text-white/80 text-sm mt-3"></p>
        </div>

        <!-- 快速添加 -->
        <div class="glass p-6">
          <h2 class="text-xl font-semibold mb-4">✏️ 快速添加</h2>
          <div class="flex gap-3">
            <input id="nameInput" type="text" placeholder="输入姓名（支持批量：回车/空格/逗号/分号/中文逗号分隔；可加标签：张三|一班）" class="flex-1 px-4 py-3 rounded-full bg-white/10 text-white placeholder-white/60 border border-white/20 focus:outline-none">
            <button id="btnAdd" class="btn btn-primary">添加</button>
          </div>
          <p class="text-xs text-white/70 mt-2">支持格式示例：
            <span class="underline">张三, 李四, 王五</span> 或多行，每行一个；可选标签：<span class="underline">李四|二班</span>
          </p>
        </div>

        <!-- 点名显示区 -->
        <div class="glass p-8">
          <div id="display" class="min-h-[180px] flex items-center justify-center text-center">
            <p class="text-white/70">点击下方按钮开始点名…</p>
          </div>
          <div class="mt-6 flex flex-wrap justify-center gap-3">
            <button id="btnDraw" class="btn btn-primary text-lg px-8 py-4">🎯 开始点名</button>
            <button id="btnMulti" class="btn btn-ghost">批量抽取</button>
            <button id="btnFullscreen" class="btn btn-ghost">全屏显示</button>
            <button id="btnAutoCycle" class="btn btn-ghost">循环点名</button>
          </div>
        </div>

        <!-- 高级设置 -->
        <div class="glass p-6">
          <h2 class="text-xl font-semibold mb-4">⚙️ 高级设置</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <label class="flex items-center gap-2"><input id="optNoRepeat" type="checkbox" class="scale-110" checked><span>不重复抽取</span></label>
            <label class="flex items-center gap-2"><input id="optAnimation" type="checkbox" class="scale-110" checked><span>启用滚动动画</span></label>
            <label class="flex items-center gap-2"><input id="optFireworks" type="checkbox" class="scale-110" checked><span>庆祝烟花</span></label>
            <label class="flex items-center gap-2"><input id="optSound" type="checkbox" class="scale-110"><span>音效提示</span></label>
            <label class="flex items-center gap-2"><input id="optFair" type="checkbox" class="scale-110" checked><span>公平模式（抽中过权重降低）</span></label>
            <div class="flex items-center gap-2">
              <span class="whitespace-nowrap">批量人数</span>
              <input id="optBatch" type="number" min="1" value="5" class="w-20 px-3 py-2 rounded-lg bg-white/10 border border-white/20">
            </div>
            <div class="flex items-center gap-2">
              <span class="whitespace-nowrap">循环间隔(秒)</span>
              <input id="optInterval" type="number" min="2" value="5" class="w-24 px-3 py-2 rounded-lg bg-white/10 border border-white/20">
            </div>
            <div class="flex items-center gap-2">
              <span class="whitespace-nowrap">随机种子</span>
              <input id="optSeed" type="text" placeholder="留空为真随机" class="flex-1 px-3 py-2 rounded-lg bg-white/10 border border-white/20">
            </div>
          </div>
          <div class="mt-4 flex flex-wrap gap-2 items-center">
            <span class="text-sm">筛选标签（只在包含所选标签时参与）：</span>
            <input id="filterTag" type="text" placeholder="如：一班" class="px-3 py-2 rounded-lg bg-white/10 border border-white/20">
            <button id="btnApplyFilter" class="btn btn-ghost">应用筛选</button>
            <button id="btnClearFilter" class="btn btn-ghost">清除筛选</button>
          </div>
        </div>
      </section>

      <!-- 右：名单/统计/历史 -->
      <aside class="space-y-6">
        <!-- 名单管理 -->
        <div class="glass p-6">
          <div class="flex items-center justify-between mb-3">
            <h2 class="text-xl font-semibold">👥 名单管理</h2>
            <span class="pill px-3 py-1 rounded-full text-sm">共 <b id="countAll">0</b> 人</span>
          </div>
          <div class="flex gap-2 mb-3">
            <input id="search" type="text" placeholder="搜索姓名/标签" class="flex-1 px-3 py-2 rounded-lg bg-white/10 border border-white/20">
            <button id="btnSort" class="btn btn-ghost text-sm">排序</button>
            <button id="btnExport" class="btn btn-ghost text-sm">导出</button>
          </div>
          <div id="list" class="max-h-[320px] overflow-y-auto space-y-2 pr-1">
            <p class="text-white/60 text-center py-4">暂无数据</p>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="glass p-6">
          <h2 class="text-xl font-semibold mb-3">📊 统计</h2>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between pill px-4 py-2 rounded-xl"><span>总抽取次数</span><b id="statTotal">0</b></div>
            <div class="flex justify-between pill px-4 py-2 rounded-xl"><span>已抽取人数</span><b id="statDrawn">0</b></div>
          </div>
          <div class="mt-3">
            <h3 class="font-semibold mb-2">Top 被抽取</h3>
            <div id="topDraw" class="space-y-1 text-sm"></div>
          </div>
        </div>

        <!-- 历史记录 -->
        <div class="glass p-6">
          <div class="flex items-center justify-between mb-2">
            <h2 class="text-xl font-semibold">📜 历史记录</h2>
            <button id="btnClearHistory" class="text-white/70 hover:text-white text-sm">清除</button>
          </div>
          <div id="history" class="max-h-[220px] overflow-y-auto space-y-2 pr-1">
            <p class="text-white/60 text-center py-4">暂无记录</p>
          </div>
        </div>
      </aside>
    </div>
  </div>

  <!-- 表格列选择模态框（支持多列） -->
  <div id="modalTable" class="modal">
    <div class="modal-card glass p-6 rounded-2xl w-[min(900px,95vw)] text-white">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-2xl font-bold">选择包含姓名的列（可多选）</h3>
        <button class="btn btn-ghost" onclick="closeModal('modalTable')">关闭</button>
      </div>
      <div class="mb-2 text-sm text-white/80">勾选后点击下方“确认导入”。首行是否为表头由“首行为表头”开关决定。</div>
      <div id="tablePreview" class="bg-white/10 rounded-xl overflow-auto max-h-[50vh]"></div>
      <div class="mt-4 flex justify-end gap-2">
        <button class="btn btn-ghost" onclick="closeModal('modalTable')">取消</button>
        <button id="btnConfirmColumns" class="btn btn-primary">确认导入</button>
      </div>
    </div>
  </div>

  <!-- 粘贴导入模态框 -->
  <div id="modalPaste" class="modal">
    <div class="modal-card glass p-6 rounded-2xl w-[min(700px,95vw)] text-white">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-2xl font-bold">粘贴导入名单</h3>
        <button class="btn btn-ghost" onclick="closeModal('modalPaste')">关闭</button>
      </div>
      <textarea id="pasteArea" class="w-full h-[45vh] p-3 rounded-xl bg-white/10 border border-white/20" placeholder="每行一个姓名；或用逗号/空格/分号/中文逗号分隔；可写标签：张三|一班"></textarea>
      <div class="mt-4 flex justify-end gap-2">
        <button class="btn btn-ghost" onclick="closeModal('modalPaste')">取消</button>
        <button id="btnPasteImport" class="btn btn-primary">导入</button>
      </div>
    </div>
  </div>

  <audio id="ding" preload="auto">
    <source src="https://cdn.jsdelivr.net/gh/naptha/tinysound@gh-pages/ding.mp3" type="audio/mpeg">
  </audio>

  <script>
    // —— 工具方法 ——
    const $ = sel => document.querySelector(sel);
    const $$ = sel => Array.from(document.querySelectorAll(sel));
    const byId = id => document.getElementById(id);

    // 动态加载 XLSX（SheetJS），返回 Promise
    function ensureXLSX(){
      return new Promise((resolve, reject)=>{
        if(window.XLSX) return resolve(window.XLSX);
        const urls = [
          'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js',
          'https://unpkg.com/xlsx/dist/xlsx.full.min.js'
        ];
        let i = 0;
        function tryLoad(){
          if(i>=urls.length){ reject(new Error('无法加载 XLSX 库，请检查网络或允许外部脚本加载。')); return; }
          const s = document.createElement('script');
          s.src = urls[i++];
          s.onload = ()=>{ if(window.XLSX) resolve(window.XLSX); else tryLoad(); };
          s.onerror = ()=>{ s.remove(); tryLoad(); };
          document.head.appendChild(s);
        }
        tryLoad();
      });
    }

    // 简单 PRNG（支持种子）
    function mulberry32(a){return function(){let t=a+=0x6D2B79F5;t=Math.imul(t^t>>>15,t|1);t^=t+Math.imul(t^t>>>7,t|61);return((t^t>>>14)>>>0)/4294967296}}
    function seededRandom(seed){ if(!seed) return Math.random; let h=0; for(let i=0;i<seed.length;i++){h=(h<<5)-h+seed.charCodeAt(i)|0} return mulberry32(h>>>0) }

    // 解析姓名，支持多分隔符与标签（name|tag）
    function parseNames(input){
      if(!input) return [];
      let raw = Array.isArray(input) ? input.join('\n') : String(input);
      const parts = raw.split(/[\n,;，；\s]+/).map(s=>s.trim()).filter(Boolean);
      // 输出对象：{ name, tag }
      return parts.map(s=>{
        const [name, tag] = s.split('|').map(v=>v && v.trim());
        return { name, tag: tag||'' };
      });
    }

    // 去重（按 name + tag）
    function uniqPeople(arr){
      const set = new Set();
      const out = [];
      for(const p of arr){
        const key = `${p.name}__${p.tag||''}`;
        if(!set.has(key)) { set.add(key); out.push(p); }
      }
      return out;
    }

    // Fisher-Yates 洗牌（使用随机函数注入）
    function shuffle(array, rnd=Math.random){
      const a = array.slice();
      for(let i=a.length-1;i>0;i--){
        const j = Math.floor(rnd()*(i+1));
        [a[i],a[j]]=[a[j],a[i]];
      }
      return a;
    }

    // 状态管理
    const state = {
      people: [], // {name, tag}
      drawn: new Set(),
      stats: {}, // name__tag: times
      history: [], // {name, tag, time}
      filterTag: '',
      rnd: Math.random,
      timer: null,
    };

    // 本地存储
    const LS_KEY = 'unified-picker-v1';
    function save(){
      const data = {people: state.people, drawn: Array.from(state.drawn), stats: state.stats, history: state.history, filterTag: state.filterTag};
      localStorage.setItem(LS_KEY, JSON.stringify(data));
    }
    function load(){
      try{ const data = JSON.parse(localStorage.getItem(LS_KEY)||'{}');
        state.people = Array.isArray(data.people)?data.people:[];
        state.drawn = new Set(Array.isArray(data.drawn)?data.drawn:[]);
        state.stats = data.stats||{};
        state.history = Array.isArray(data.history)?data.history:[];
        state.filterTag = data.filterTag||'';
      }catch(e){ console.warn('load storage fail', e); }
    }

    // UI 刷新
    function renderList(){
      const box = byId('list');
      const q = byId('search').value.trim();
      const filtered = state.people.filter(p=>{
        const tagPass = !state.filterTag || (p.tag||'')===state.filterTag;
        const s = `${p.name} ${(p.tag||'')}`;
        const searchPass = !q || s.includes(q);
        return tagPass && searchPass;
      });
      byId('countAll').textContent = state.people.length;
      if(filtered.length===0){ box.innerHTML = '<p class="text-white/60 text-center py-4">暂无数据</p>'; return; }
      box.innerHTML = '';
      for(const p of filtered){
        const key = `${p.name}__${p.tag||''}`;
        const times = state.stats[key]||0;
        const drawned = state.drawn.has(key);
        const row = document.createElement('div');
        row.className = 'name-item rounded-xl px-3 py-2 flex items-center justify-between gap-3';
        row.innerHTML = `
          <div class="flex items-center gap-2 min-w-0">
            <input type="checkbox" class="chk person shrink-0">
            <div class="truncate"><span class="font-semibold">${escapeHtml(p.name)}</span> ${p.tag?`<span class='text-xs opacity-80 ml-1'>| ${escapeHtml(p.tag)}</span>`:''}</div>
          </div>
          <div class="flex items-center gap-2 text-xs opacity-90">
            <span class="px-2 py-1 rounded pill">抽中：<b>${times}</b></span>
            ${drawned?'<span class="px-2 py-1 rounded pill">已抽</span>':''}
            <button class="text-white/80 hover:text-white" title="删除">✖</button>
          </div>`;
        row.querySelector('button').onclick = ()=>{ removePerson(p); };
        row.querySelector('.chk').onclick = e=>{ row.classList.toggle('ring-2', e.target.checked); };
        box.appendChild(row);
      }
    }

    function renderStats(){
      const total = Object.values(state.stats).reduce((a,b)=>a+b,0);
      byId('statTotal').textContent = total;
      byId('statDrawn').textContent = state.drawn.size;
      // top
      const entries = Object.entries(state.stats).sort((a,b)=>b[1]-a[1]).slice(0,8);
      const wrap = byId('topDraw');
      wrap.innerHTML = entries.length? '' : '<p class="text-white/60">暂无</p>';
      for(const [key, n] of entries){
        const [name, tag] = key.split('__');
        const item = document.createElement('div');
        item.className = 'pill px-3 py-1 rounded-xl flex justify-between';
        item.innerHTML = `<span>${escapeHtml(name)} ${tag?`<span class='opacity-70 text-xs'>| ${escapeHtml(tag)}</span>`:''}</span><b>${n}</b>`;
        wrap.appendChild(item);
      }
    }

    function renderHistory(){
      const box = byId('history');
      if(state.history.length===0){ box.innerHTML = '<p class="text-white/60 text-center py-4">暂无记录</p>'; return; }
      box.innerHTML = '';
      for(const h of state.history.slice(0,60)){
        const row = document.createElement('div');
        row.className = 'pill px-3 py-2 rounded-xl flex justify-between items-center';
        row.innerHTML = `<div>${escapeHtml(h.name)} ${h.tag?`<span class='opacity-70 text-xs'>| ${escapeHtml(h.tag)}</span>`:''}</div><div class='opacity-80 text-xs'>${h.time}</div>`;
        box.appendChild(row);
      }
    }

    function setDisplay(content){
      const el = byId('display');
      el.innerHTML='';
      el.appendChild(content);
    }

    // 转义
    function escapeHtml(str){ return String(str).replace(/[&<>"']/g, s=>({"&":"&amp;","<":"&lt;",">":"&gt;","\"":"&quot;","'":"&#39;"}[s])) }

    // —— 名单增删改 ——
    function addPeople(list){
      const merged = uniqPeople([...state.people, ...list.filter(p=>p.name)]);
      const added = merged.length - state.people.length;
      state.people = merged;
      save();
      renderList();
      return added;
    }

    function removePerson(p){
      const key = `${p.name}__${p.tag||''}`;
      state.people = state.people.filter(x=>!(x.name===p.name && (x.tag||'')===(p.tag||'')));
      state.drawn.delete(key);
      delete state.stats[key];
      save();
      renderList();
      renderStats();
    }

    function clearAll(){
      state.people=[]; state.drawn.clear(); state.stats={}; state.history=[]; save();
      renderList(); renderStats(); renderHistory();
    }

    // —— 导入处理（修复“只导入一个名字”问题：统一多列多行批量解析） ——
    const tableSel = { data:null, headers:[], selected:new Set() };

    function handleFiles(files){
      if(!files || files.length===0) return;
      byId('importStatus').textContent = '正在导入…';
      const tasks = Array.from(files).map(file=>new Promise((resolve)=>{
        const ext = file.name.split('.').pop().toLowerCase();
        if(ext==='txt'){
          file.text().then(text=>{ resolve(parseNames(text)); }).catch(()=>resolve([]));
        } else if(ext==='csv'){
          file.text().then(text=>{
            Papa.parse(text,{header: byId('hasHeader').checked, skipEmptyLines:true, complete:(res)=>{
              // res.data 可能是对象数组或二维数组
              showTableSelector(res.data);
              resolve([]); // 由用户选择列后再导入
            }});
          }).catch(()=>resolve([]));
        } else if(ext==='xlsx' || ext==='xls'){
          // 动态加载 XLSX（若未加载）
          ensureXLSX().then(()=>{
            file.arrayBuffer().then(buf=>{
              try{
                const wb = XLSX.read(buf,{type:'array'});
                const sheet = wb.Sheets[wb.SheetNames[0]];
                // 使用 header:1 保证返回二维数组，后续由 hasHeader 决定首行是否为表头
                const arr = XLSX.utils.sheet_to_json(sheet,{header:1, raw:false});
                showTableSelector(arr);
              }catch(e){ console.error(e); byId('importStatus').textContent='解析 XLSX 失败'; setTimeout(()=>byId('importStatus').textContent='',2500); }
              resolve([]);
            }).catch(()=>{ byId('importStatus').textContent='读取文件失败'; setTimeout(()=>byId('importStatus').textContent='',2500); resolve([]); });
          }).catch(err=>{
            console.warn('XLSX load fail', err);
            byId('importStatus').textContent = '加载 XLSX 库失败，请检查网络或允许外部脚本加载';
            setTimeout(()=>byId('importStatus').textContent='',4000);
            resolve([]);
          });
        } else { resolve([]); }
      }));

      Promise.all(tasks).then(results=>{
        const merged = results.flat();
        if(merged.length){
          const added = addPeople(merged);
          byId('importStatus').textContent = `成功导入 ${added} 人（当前共 ${state.people.length} 人）`;
          setTimeout(()=>byId('importStatus').textContent='', 2500);
        } else {
          // 等待用户在列选择器中确认
          byId('importStatus').textContent = '如为表格类文件，请在弹窗中选择包含姓名的列';
          setTimeout(()=>byId('importStatus').textContent='', 3500);
        }
      });
    }

    function showTableSelector(data){
      if(!data || !data.length) return;
      tableSel.data = data;
      const hasHeader = byId('hasHeader').checked;
      let headers=[];
      if(Array.isArray(data[0])){ // 二维数组
        const cols = Math.max(...data.map(r=>Array.isArray(r)?r.length:0));
        headers = Array.from({length: cols}, (_,i)=> hasHeader? ( (Array.isArray(data[0])? (data[0][i]||`列${i+1}`) : `列${i+1}`) ) : `列${i+1}`);
      } else if(typeof data[0]==='object'){
        headers = Object.keys(data[0]);
      }
      tableSel.headers = headers;
      tableSel.selected = new Set(headers.map((_,i)=>i)); // 默认全选

      const table = document.createElement('table');
      table.className = 'w-full text-sm border-collapse';
      const thead = document.createElement('thead');
      const trh = document.createElement('tr');
      headers.forEach((h,i)=>{
        const th = document.createElement('th');
        th.className='bg-white/10 p-2 border-b border-white/10 sticky top-0 backdrop-blur text-left';
        th.innerHTML = `<label class="flex items-center gap-2"><input class='colchk' type='checkbox' data-idx='${i}' checked><span>${escapeHtml(h)}</span></label>`;
        trh.appendChild(th);
      });
      thead.appendChild(trh); table.appendChild(thead);
      const tbody = document.createElement('tbody');
      const rows = data.slice(0,30); // 预览前 30 行
      for(const r of rows){
        const tr = document.createElement('tr');
        headers.forEach((h,i)=>{
          const td = document.createElement('td');
          td.className='p-2 border-b border-white/10 whitespace-nowrap';
          let val='';
          if(Array.isArray(r)) val = r[i] ?? '';
          else val = r[h] ?? '';
          td.textContent = val;
          tr.appendChild(td);
        });
        tbody.appendChild(tr);
      }
      table.appendChild(tbody);
      const wrap = byId('tablePreview');
      wrap.innerHTML=''; wrap.appendChild(table);
      $('#modalTable').classList.add('show');
      // 绑定列复选框事件
      $$('#tablePreview .colchk').forEach(ch=>{
        ch.addEventListener('change', e=>{
          const idx = Number(e.target.dataset.idx); if(e.target.checked) tableSel.selected.add(idx); else tableSel.selected.delete(idx);
        });
      });
    }

    function confirmColumns(){
      const sel = Array.from(tableSel.selected);
      if(!sel.length){ closeModal('modalTable'); return; }
      const data = tableSel.data;
      const hasHeader = byId('hasHeader').checked;
      let list = [];
      if(Array.isArray(data[0])){
        const start = hasHeader ? 1 : 0;
        for(let i=start;i<data.length;i++){
          const row = data[i]||[];
          for(const c of sel){ const v = (row[c]||'').toString().trim(); if(v) list.push({name:v, tag:''}); }
        }
      } else {
        for(const row of data){ for(const h of sel.map(i=>tableSel.headers[i])){ const v=(row[h]||'').toString().trim(); if(v) list.push({name:v, tag:''}); } }
      }
      const added = addPeople(list);
      byId('importStatus').textContent = `成功导入 ${added} 人（当前共 ${state.people.length} 人）`;
      setTimeout(()=>byId('importStatus').textContent='', 2500);
      closeModal('modalTable');
    }

    function closeModal(id){ byId(id).classList.remove('show'); }

    // —— 导出 ——
    function exportData(){
      const rows = state.people.map(p=>[p.name, p.tag]);
      const csv = ['姓名,标签', ...rows.map(r=>r.map(x=>`"${(x||'').replace(/"/g,'""')}"`).join(','))].join('\n');
      const blob = new Blob([csv],{type:'text/csv;charset=utf-8'});
      const a = document.createElement('a'); a.href=URL.createObjectURL(blob); a.download='名单导出.csv'; a.click(); URL.revokeObjectURL(a.href);
    }

    // —— 抽取逻辑 ——
    function candidatePool(){
      const tag = state.filterTag;
      let pool = state.people.filter(p=>!tag || (p.tag||'')===tag);
      const noRepeat = byId('optNoRepeat').checked;
      if(noRepeat){ pool = pool.filter(p=> !state.drawn.has(`${p.name}__${p.tag||''}`) ); }
      return pool;
    }

    function fairWeight(key){ // 次数越多权重越低
      const n = state.stats[key]||0; return 1/Math.pow(1.4,n); // 1, 0.71, 0.51, 0.36 …
    }

    function drawOne(){
      const pool = candidatePool();
      if(pool.length===0){
        // 自动重置一次
        state.drawn.clear(); save(); renderStats();
        return drawOne();
      }
      const fair = byId('optFair').checked;
      if(!fair){
        const idx = Math.floor(state.rnd()*pool.length);
        return pool[idx];
      }
      // 加权随机
      const keys = pool.map(p=>`${p.name}__${p.tag||''}`);
      const weights = keys.map(k=>fairWeight(k));
      const sum = weights.reduce((a,b)=>a+b,0);
      let r = state.rnd()*sum;
      for(let i=0;i<pool.length;i++){ if((r-=weights[i])<=0) return pool[i]; }
      return pool[pool.length-1];
    }

    function afterDraw(person){
      const key = `${person.name}__${person.tag||''}`;
      state.stats[key] = (state.stats[key]||0)+1;
      state.drawn.add(key);
      state.history.unshift({name:person.name, tag:person.tag||'', time: new Date().toLocaleString()});
      state.history = state.history.slice(0,200);
      save(); renderStats(); renderHistory(); renderList();
    }

    function showResult(names){
      const wrap = document.createElement('div');
      if(byId('optAnimation').checked && names.length===1){
        const ticker = document.createElement('div');
        ticker.className='h-32 overflow-hidden text-5xl font-extrabold flex flex-col items-center justify-center';
        const roll = document.createElement('div'); roll.className='ticker';
        const pool = shuffle(state.people, state.rnd).slice(0,Math.min(40, state.people.length));
        // 修复：确保 names 中使用的是姓名字符串
        const seq = [...pool.map(p=>p.name), ...names.map(n=>n.name)];
        for(const s of seq){ const p = document.createElement('div'); p.className='py-1'; p.textContent = s; roll.appendChild(p); }
        ticker.appendChild(roll); wrap.appendChild(ticker);
        setTimeout(()=>{ setDisplay(resultBadge(names)); celebrate(); playDing(); }, 1000);
        setDisplay(wrap);
      } else {
        setDisplay(resultBadge(names)); celebrate(); playDing();
      }
    }

    function resultBadge(names){
      const box = document.createElement('div');
      box.className='fade-in flex flex-wrap gap-3 items-center justify-center';
      for(const n of names){
        const tag = n.tag?`<span class='text-xs opacity-80 ml-1'>| ${escapeHtml(n.tag)}</span>`:'';
        const div = document.createElement('div');
        div.className='px-5 py-3 rounded-2xl text-3xl font-extrabold bg-white/90 text-gray-800 shadow-lg';
        div.innerHTML = `${escapeHtml(n.name)} ${tag}`;
        box.appendChild(div);
      }
      return box;
    }

    function playDing(){ if(byId('optSound').checked){ byId('ding').currentTime=0; byId('ding').play().catch(()=>{}); } }

    function celebrate(){ if(!byId('optFireworks').checked) return; const n=12; for(let i=0;i<n;i++){ const s=document.createElement('span'); const size=6+Math.random()*10; Object.assign(s.style,{position:'fixed',left:(Math.random()*100)+'%',top:(40+Math.random()*40)+'%',width:size+'px',height:size+'px',background:'#fff',borderRadius:'50%',boxShadow:'0 0 10px #fff',opacity:.9}); document.body.appendChild(s); setTimeout(()=>s.remove(), 700);} }

    function drawOnce(){
      if(state.people.length===0){ showToast('名单为空，请先导入或添加'); return; }
      const person = drawOne();
      if(!person) { showToast('可抽取名单为空，已自动重置未抽状态'); return drawOnce(); }
      afterDraw(person);
      showResult([person]);
    }

    function drawBatch(){
      const n = Math.max(1, Math.floor(Number(byId('optBatch').value)||1));
      const res = [];
      for(let i=0;i<n;i++){ const p = drawOne(); if(p){ afterDraw(p); res.push(p); } }
      showResult(res);
    }

    function toggleFullscreen(){
      const el = document.documentElement;
      if(!document.fullscreenElement){ el.requestFullscreen?.(); } else { document.exitFullscreen?.(); }
    }

    function startCycle(){
      if(state.timer){ clearInterval(state.timer); state.timer=null; byId('btnAutoCycle').textContent='循环点名'; return; }
      const sec = Math.max(2, Number(byId('optInterval').value)||5);
      state.timer = setInterval(()=>drawOnce(), sec*1000);
      byId('btnAutoCycle').textContent='停止循环';
    }

    // —— 事件绑定 ——
    function bind(){
      byId('fileInput').addEventListener('change', e=>{ handleFiles(e.target.files); e.target.value=''; });
      byId('btnPaste').addEventListener('click', ()=>{ byId('modalPaste').classList.add('show'); });
      byId('btnPasteImport').addEventListener('click', ()=>{ const list=parseNames(byId('pasteArea').value); const added=addPeople(list); byId('pasteArea').value=''; closeModal('modalPaste'); showToast(`导入 ${added} 人`); });
      byId('btnAdd').addEventListener('click', ()=>{ const added=addPeople(parseNames(byId('nameInput').value)); byId('nameInput').value=''; showToast(`添加 ${added} 人`); });
      byId('nameInput').addEventListener('keypress', e=>{ if(e.key==='Enter'){ byId('btnAdd').click(); }});
      byId('btnTemplate').addEventListener('click', ()=>{
        const csv = '姓名,标签\n张三,一班\n李四,二班\n王五,';
        const blob = new Blob([csv],{type:'text/csv;charset=utf-8'});
        const a=document.createElement('a'); a.href=URL.createObjectURL(blob); a.download='名单模板.csv'; a.click(); URL.revokeObjectURL(a.href);
      });
      byId('btnClearAll').addEventListener('click', ()=>{ if(confirm('确认清空所有数据？')) clearAll(); });
      byId('btnExport').addEventListener('click', exportData);
      byId('btnSort').addEventListener('click', ()=>{ state.people.sort((a,b)=>a.name.localeCompare(b.name,'zh')); save(); renderList(); });
      byId('btnClearHistory').addEventListener('click', ()=>{ state.history=[]; save(); renderHistory(); });
      byId('btnApplyFilter').addEventListener('click', ()=>{ state.filterTag = byId('filterTag').value.trim(); save(); renderList(); });
      byId('btnClearFilter').addEventListener('click', ()=>{ state.filterTag=''; byId('filterTag').value=''; save(); renderList(); });

      byId('btnDraw').addEventListener('click', drawOnce);
      byId('btnMulti').addEventListener('click', drawBatch);
      byId('btnFullscreen').addEventListener('click', toggleFullscreen);
      byId('btnAutoCycle').addEventListener('click', startCycle);

      byId('btnConfirmColumns').addEventListener('click', confirmColumns);

      byId('optSeed').addEventListener('input', e=>{ state.rnd = seededRandom(e.target.value.trim()); });
      byId('search').addEventListener('input', renderList);
      document.addEventListener('keydown', e=>{ if(e.key===' '){ e.preventDefault(); drawOnce(); } });

      // 测试按钮
      byId('btnTestPreview').addEventListener('click', ()=>{
        // 模拟二位数组（含表头）
        const sample = [['姓名','班级'],['张三','一班'],['李四','二班'],['王五','一班']];
        showTableSelector(sample);
      });
      byId('btnTestXLSX').addEventListener('click', ()=>{
        byId('importStatus').textContent='尝试加载 XLSX 库…';
        ensureXLSX().then(()=>{ byId('importStatus').textContent='XLSX 库加载成功'; setTimeout(()=>byId('importStatus').textContent='',2000); }).catch(err=>{ byId('importStatus').textContent='加载 XLSX 库失败：'+(err.message||err); setTimeout(()=>byId('importStatus').textContent='',4000); });
      });
    }

    function showToast(msg){
      const n = document.createElement('div');
      n.className='fixed left-1/2 -translate-x-1/2 top-4 bg-black/70 text-white px-4 py-2 rounded-xl shadow-lg fade-in z-50';
      n.textContent = msg; document.body.appendChild(n); setTimeout(()=>n.remove(),2000);
    }

    // —— 启动 ——
    load();
    bind();
    if(byId('optSeed').value) state.rnd = seededRandom(byId('optSeed').value);
    renderList(); renderStats(); renderHistory();
    if(state.filterTag) byId('filterTag').value = state.filterTag;
  </script>
</body>
</html>
