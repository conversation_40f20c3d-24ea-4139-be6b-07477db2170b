<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>家国情怀：从历史传承到时代担当 - 交互式PPT</title>
    <script src="https://unpkg.byted-static.com/coze/space_ppt_lib/1.0.3-alpha.12/lib/tailwindcss.js"></script>
    <script src="https://unpkg.byted-static.com/chart.js/4.5.0/dist/chart.umd.js"></script>
    <script src="https://unpkg.byted-static.com/fortawesome/fontawesome-free/6.7.2/js/all.min.js" data-auto-replace-svg="nest"></script>
    <link href="https://lf-code-agent.coze.cn/obj/x-ai-cn/fonts/google/google-all-fonts.css" rel="stylesheet"/>
    <style>
        .font-noto { font-family: 'Noto Sans SC', sans-serif; }
        body { margin: 0; padding: 0; overflow: hidden; background: #000; }
        
        /* PPT容器样式 */
        .ppt-container { 
            width: 100vw; 
            height: 100vh; 
            position: relative; 
        }
        
        /* 单页幻灯片样式 */
        .slide { 
            width: 100%; 
            height: 100%; 
            position: absolute; 
            top: 0; 
            left: 0; 
            opacity: 0; 
            transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            transform: scale(0.95);
        }
        .slide.active { 
            opacity: 1; 
            z-index: 10; 
            transform: scale(1);
        }
        .slide.next { 
            transform: translateX(100%);
        }
        .slide.prev { 
            transform: translateX(-100%);
        }
        
        /* 控制栏样式 */
        .control-bar {
            position: fixed;
            bottom: 20px;
            left: 0;
            right: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            padding: 10px 20px;
            background: rgba(0,0,0,0.7);
            border-radius: 8px;
            max-width: 800px;
            margin: 0 auto;
            backdrop-filter: blur(5px);
        }
        .control-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #1e90ff;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        .control-btn:hover {
            background: #0b6bcb;
            transform: scale(1.1);
        }
        .control-btn.audio-btn {
            background: #ff4757;
        }
        .control-btn.audio-btn:hover {
            background: #e02e3e;
        }
        .progress-container {
            flex: 1;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            overflow: hidden;
            cursor: pointer;
        }
        .progress-bar {
            height: 100%;
            background: #1e90ff;
            width: 0%;
            transition: width 0.3s ease;
        }
        .slide-info {
            color: white;
            font-size: 14px;
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        /* 逐点显示元素样式 */
        .reveal-item {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .reveal-item.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 音频播放按钮样式 */
        .audio-play-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255, 71, 87, 0.8);
            color: white;
            cursor: pointer;
            margin-left: 8px;
            transition: all 0.3s ease;
            vertical-align: middle;
        }
        .audio-play-btn:hover {
            background: #ff4757;
            transform: scale(1.1);
        }
        .audio-play-btn.playing {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7); }
            70% { transform: scale(1); box-shadow: 0 0 0 10px rgba(255, 71, 87, 0); }
            100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 71, 87, 0); }
        }
        
        /* 通用动画样式 */
        @keyframes fadeIn { 0% { opacity: 0; } 100% { opacity: 1; } }
        @keyframes slideUp { 0% { transform: translateY(50px); opacity: 0; } 100% { transform: translateY(0); opacity: 1; } }
        @keyframes scaleIn { 0% { transform: scale(0.9); opacity: 0; } 100% { transform: scale(1); opacity: 1; } }
        @keyframes pulseHighlight { 0% { background-color: rgba(255, 215, 0, 0.3); } 50% { background-color: rgba(255, 215, 0, 0.6); } 100% { background-color: rgba(255, 215, 0, 0.3); } }
        @keyframes underlinePulse { 0% { text-decoration-thickness: 2px; opacity: 1; } 50% { text-decoration-thickness: 4px; opacity: 0.7; } 100% { text-decoration-thickness: 2px; opacity: 1; } }
        
        .animate-fade-in { animation: fadeIn 0.8s ease-out forwards; }
        .animate-slide-up { animation: slideUp 0.8s ease-out forwards; }
        .animate-scale-in { animation: scaleIn 0.8s ease-out forwards; }
        .animate-pulse-highlight { animation: pulseHighlight 2s infinite; padding: 0 4px; border-radius: 2px; }
        .animate-underline-pulse { animation: underlinePulse 1.5s infinite; text-decoration: underline; text-decoration-color: #ff4757; }
        
        .text-shadow { text-shadow: 0 2px 10px rgba(0, 0, 0, 0.6); }
        .bg-radial { background-image: radial-gradient(circle, var(--tw-gradient-stops)); }
        
        /* 名言样式 */
        .quote-container {
            position: relative;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .quote-container:hover {
            transform: translateY(-5px);
        }
        .quote-text {
            font-style: italic;
            position: relative;
            z-index: 2;
        }
        .quote-mark {
            position: absolute;
            font-size: 80px;
            color: rgba(30, 144, 255, 0.1);
            z-index: 1;
        }
        .quote-mark.left {
            top: -20px;
            left: -10px;
        }
        .quote-mark.right {
            bottom: -60px;
            right: -10px;
        }
    </style>
</head>
<body class="font-noto">
    <!-- 音频元素（隐藏） -->
    <audio id="audioPlayer" preload="none">
        <source src="" type="audio/mpeg">
    </audio>
    
    <!-- PPT容器 -->
    <div class="ppt-container" id="pptContainer">
        <!-- 第1页：封面 -->
        <div class="slide active" id="slide1" data-reveal-count="2">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#0d1a26]">
                <img alt="蜿蜒于山峦间的长城剪影，展现历史厚重感" class="absolute inset-0 w-full h-full z-0 opacity-40 object-cover object-top animate-scale-in" src="https://s.coze.cn/image/mRjvTG5ZzMM/"/>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[900px] h-[900px] bg-radial from-[#1e90ff]/10 to-transparent rounded-full pointer-events-none z-10"></div>
                <div class="relative z-20 flex flex-col items-center justify-center text-center text-white p-12">
                    <h1 class="text-6xl font-black tracking-wider text-shadow reveal-item" style="background: linear-gradient(90deg, #ffffff, #ffd700); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        家国情怀：从历史传承到时代担当
                    </h1>
                    <div class="w-40 h-1 bg-[#ff4757] my-8 reveal-item"></div>
                    <p class="text-2xl font-light tracking-widest text-shadow max-w-4xl reveal-item" data-delay="200">
                        爱国主义精神、民族自豪感与社会责任感的当代诠释
                    </p>
                </div>
                <div class="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-[#2ed573] via-[#1e90ff] to-[#ff4757] z-20"></div>
            </div>
        </div>

        <!-- 第2页：目录 -->
        <div class="slide" id="slide2" data-reveal-count="8">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa] p-16">
                <div class="absolute top-0 right-0 w-1/2 h-full pointer-events-none z-0">
                    <svg fill="none" height="100%" viewbox="0 0 640 720" width="100%" xmlns="http://www.w3.org/2000/svg">
                        <path d="M640 0L400 720" stroke="#1e90ff" stroke-opacity="0.1" stroke-width="2"></path>
                        <path d="M550 0L350 720" stroke="#ff4757" stroke-opacity="0.1" stroke-width="2"></path>
                        <path d="M600 0L450 720" stroke="#2ed573" stroke-opacity="0.1" stroke-width="2"></path>
                    </svg>
                </div>
                <header class="flex-shrink-0 w-full mb-12 z-10">
                    <h1 class="text-5xl font-bold text-[#2d3436] reveal-item">目录</h1>
                    <div class="w-24 h-1.5 bg-[#1e90ff] mt-3 rounded-full reveal-item"></div>
                </header>
                <main class="flex-grow w-full flex items-center min-h-0 z-10">
                    <div class="w-full">
                        <ul class="toc-list columns-2 gap-x-20 text-2xl text-[#2d3436] leading-[2.5]">
                            <li class="flex items-center break-inside-avoid mb-4 reveal-item" data-delay="100">家国情怀的内涵解析</li>
                            <li class="flex items-center break-inside-avoid mb-4 reveal-item" data-delay="200">历史长河中的家国记忆</li>
                            <li class="flex items-center break-inside-avoid mb-4 reveal-item" data-delay="300">民族复兴的辉煌成就</li>
                            <li class="flex items-center break-inside-avoid mb-4 reveal-item animate-underline-pulse" data-delay="400">榜样力量：家国担当的践行者</li>
                            <li class="flex items-center break-inside-avoid mb-4 reveal-item" data-delay="500">家国情怀的教育实践</li>
                            <li class="flex items-center break-inside-avoid mb-4 reveal-item" data-delay="600">传统节日中的家国情怀</li>
                            <li class="flex items-center break-inside-avoid mb-4 reveal-item" data-delay="700">科技领域的家国情怀</li>
                            <li class="flex items-center break-inside-avoid mb-4 reveal-item" data-delay="800">结语：让家国情怀照亮前行之路</li>
                        </ul>
                    </div>
                </main>
            </div>
        </div>

        <!-- 第3页：家国情怀的内涵解析 -->
        <div class="slide" id="slide3" data-reveal-count="5">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa] p-16">
                <div class="absolute bottom-8 right-8 text-[120px] font-black text-[#1e90ff]/5 select-none pointer-events-none -rotate-12 z-0 animate-scale-in">
                    <i class="fas fa-landmark"></i>
                </div>
                <header class="flex-shrink-0 w-full mb-10 z-10">
                    <h1 class="text-4xl font-bold text-[#2d3436] reveal-item">一、家国情怀的内涵解析</h1>
                    <div class="w-24 h-1.5 bg-[#1e90ff] mt-4 rounded-full reveal-item"></div>
                </header>
                <main class="flex-grow w-full flex flex-col justify-center min-h-0 z-10">
                    <div class="space-y-8">
                        <div class="flex items-start gap-6 reveal-item" data-delay="100">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#1e90ff]/10 text-[#1e90ff] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-book-open"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#1e90ff]">核心定义：</strong>个人对家庭和国家共同体的认同与热爱，是爱国主义精神的伦理基础和情感状态。
                            </div>
                        </div>
                        <div class="flex items-start gap-6 reveal-item" data-delay="200">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#2ed573]/10 text-[#2ed573] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#2d3436]">历史渊源：</strong>根植于中国氏族血缘宗法制，《礼记》<span class="animate-pulse-highlight">“五止十义”</span>奠定伦理与政治秩序统一的基础。
                            </div>
                        </div>
                        <div class="flex items-start gap-6 reveal-item" data-delay="300">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#ff4757]/10 text-[#ff4757] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#ff4757]">现代升华：</strong>习近平总书记指出
                                <span class="animate-pulse-highlight">“爱国是本分，也是职责”</span>
                                <span class="audio-play-btn" data-audio="https://example.com/audio/xi1.mp3">
                                    <i class="fas fa-volume-up"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- 第4页：历史长河中的家国记忆 -->
        <div class="slide" id="slide4" data-reveal-count="5">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#ff4757] to-[#1e90ff]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 h-full bg-gray-200">
                        <img alt="近代救亡图存浮雕群像，展现近代家国情怀实践场景" class="w-full h-full object-cover object-top animate-scale-in reveal-item" src="https://s.coze.cn/image/Rc08Wze7ztQ/"/>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-6 reveal-item">二、历史长河中的家国记忆</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 reveal-item" data-delay="100">
                                <div class="w-10 h-10 flex items-center justify-center bg-[#1e90ff] text-white rounded-lg flex-shrink-0 mt-1">
                                    <i class="fas fa-landmark"></i>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">古代融合与认同</h2>
                                    <p class="text-[#636e72]">
                                        《礼记》“五止十义”、王昭君与文成公主和亲、“凉州会盟”、土尔扈特东归
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="200">
                                <div class="w-10 h-10 flex items-center justify-center bg-[#ff4757] text-white rounded-lg flex-shrink-0 mt-1">
                                    <i class="fas fa-flag"></i>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">近代救亡与抗争</h2>
                                    <p class="text-[#636e72]">
                                        仰韶遗址发现、抗美援朝（<span class="animate-pulse-highlight">宋阿毛绝笔</span>）
                                        <span class="audio-play-btn" data-audio="https://example.com/audio/kangmei.mp3">
                                            <i class="fas fa-volume-up"></i>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第5页：民族复兴的辉煌成就 -->
        <div class="slide" id="slide5" data-reveal-count="5">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full p-12">
                    <div class="w-1/2 flex flex-col justify-center pr-10">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-8 reveal-item">三、民族复兴的辉煌成就</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 reveal-item" data-delay="100">
                                <i class="fas fa-chart-line text-[#1e90ff] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#1e90ff] animate-underline-pulse">经济腾飞</strong>：2023年GDP达<strong class="font-bold animate-pulse-highlight">126万亿元</strong>，较1952年增长<strong class="font-bold animate-pulse-highlight">223倍</strong>。</p>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="200">
                                <i class="fas fa-heart-pulse text-[#2ed573] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#2ed573] animate-underline-pulse">民生改善</strong>：农村贫困人口全部脱贫，人均预期寿命提高<strong class="font-bold animate-pulse-highlight">43.6岁</strong>。</p>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="300">
                                <i class="fas fa-satellite-dish text-[#ff4757] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#ff4757] animate-underline-pulse">科技强国</strong>："两弹一星"、载人航天、高速铁路等重大科技成就。</p>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center pl-10">
                        <div class="flex-grow relative bg-white rounded-lg shadow-lg p-6 min-h-0 reveal-item" data-delay="400">
                            <canvas id="achievementChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第6页：榜样力量 -->
        <div class="slide" id="slide6" data-reveal-count="5">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#ff4757] to-[#1e90ff]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 h-full bg-gray-200 flex items-center justify-center overflow-hidden">
                        <img alt="陈祥榕戍边英雄军装照，展现军人庄重形象" class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105 reveal-item" src="https://s.coze.cn/image/oaHiEqFHy4s/"/>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-6 reveal-item">四、榜样力量：家国担当的践行者</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 reveal-item" data-delay="100">
                                <i class="fas fa-award text-[#ff4757] mt-1.5 flex-shrink-0"></i>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">英雄烈士</h2>
                                    <p class="text-[#636e72]">陈祥榕（<span class="animate-pulse-highlight">"清澈的爱，只为中国"</span>）
                                        <span class="audio-play-btn" data-audio="https://example.com/audio/chenxiangrong.mp3">
                                            <i class="fas fa-volume-up"></i>
                                        </span>
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="200">
                                <i class="fas fa-star text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">时代楷模</h2>
                                    <p class="text-[#636e72]"><span class="animate-pulse-highlight">黄令仪</span>（芯片研发）、<span class="animate-pulse-highlight">许振超</span>（"振超效率"）。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第7页：家国情怀的教育实践 -->
        <div class="slide" id="slide7" data-reveal-count="5">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 h-full bg-gray-200">
                        <img alt="高校国防后备连军训队列，展现“行进式”户外实践" class="w-full h-full object-cover object-top reveal-item" src="https://s.coze.cn/image/DAV-MDYxA2o/"/>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-4 reveal-item">五、家国情怀的教育实践</h1>
                        <div class="w-20 h-1 bg-[#1e90ff] mb-8 rounded-full reveal-item"></div>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 reveal-item" data-delay="100">
                                <i class="fas fa-university text-[#1e90ff] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p><strong class="font-bold text-[#2d3436]">高校实践</strong>：<span class="animate-pulse-highlight">河北金融学院</span>“四大课堂”、<span class="animate-pulse-highlight">南开大学</span>“爱国三问”。</p>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="200">
                                <i class="fas fa-school text-[#1e90ff] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p><strong class="font-bold text-[#2d3436]">中小学教育</strong>：<span class="animate-pulse-highlight">红色地标打卡计划</span>、<span class="animate-pulse-highlight">宁波初中</span>“情理共生”思政课。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第8页：传统节日中的家国情怀 -->
        <div class="slide" id="slide8" data-reveal-count="6">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa] p-16">
                <div class="absolute bottom-8 right-8 text-[120px] font-black text-[#1e90ff]/5 select-none pointer-events-none -rotate-12 z-0 animate-scale-in">
                    <i class="fas fa-pagelines"></i>
                </div>
                <header class="flex-shrink-0 w-full mb-10 z-10">
                    <h1 class="text-4xl font-bold text-[#2d3436] reveal-item">六、传统节日中的家国情怀</h1>
                    <div class="w-24 h-1.5 bg-[#1e90ff] mt-4 rounded-full reveal-item"></div>
                </header>
                <main class="flex-grow w-full flex flex-col justify-center min-h-0 z-10">
                    <div class="space-y-8">
                        <div class="flex items-start gap-6 reveal-item" data-delay="100">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#1e90ff]/10 text-[#1e90ff] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-moon"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#1e90ff]">春节</strong>：中华民族最隆重的传统节日，承载辞旧迎新愿望，蕴含家庭团聚伦理观念。
                            </div>
                        </div>
                        <div class="flex items-start gap-6 reveal-item" data-delay="200">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#2ed573]/10 text-[#2ed573] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-flag"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#2ed573]">端午节</strong>：起源于纪念爱国诗人屈原，赛龙舟、吃粽子等习俗展现团结协作民族精神。
                                <span class="audio-play-btn" data-audio="https://example.com/audio/quyuan.mp3">
                                    <i class="fas fa-volume-up"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex items-start gap-6 reveal-item" data-delay="300">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#ff4757]/10 text-[#ff4757] text-2xl rounded-full flex-shrink-0 mt-1">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#ff4757]">中秋节</strong>：以月之圆兆人之团圆，寄托对家人思念和国家统一渴望，是民族凝聚力象征。
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- 第9页：科技领域的家国情怀 -->
        <div class="slide" id="slide9" data-reveal-count="6">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full p-12">
                    <div class="w-1/2 flex flex-col justify-center pr-10">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-8 reveal-item">七、科技领域的家国情怀</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 reveal-item" data-delay="100">
                                <i class="fas fa-microchip text-[#1e90ff] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#1e90ff] animate-underline-pulse">两弹一星元勋</strong>：钱学森、邓稼先等放弃国外优越条件，回国投身国防科技事业。
                                    <span class="audio-play-btn" data-audio="https://example.com/audio/qianxuesen.mp3">
                                        <i class="fas fa-volume-up"></i>
                                    </span>
                                </p>
                            </div>
                            <div class="flex items-start gap-4 reveal-item" data-delay="200">
                                <i class="fas fa-rocket text-[#2ed573] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#2ed573] animate-underline-pulse">航天人</strong>：从"东方红一号"到"嫦娥探月"，中国航天人不断刷新中国高度。</p>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center pl-10">
                        <div class="flex-grow relative bg-white rounded-lg shadow-lg p-6 min-h-0 reveal-item" data-delay="300">
                            <canvas id="techAchievementChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第10页：名言警句集锦 -->
      <!-- 第10页：名言警句集锦 -->
<div class="slide" id="slide10" data-reveal-count="9">
    <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#0d1a26]">
        <!-- 背景优化 -->
        <img alt="水墨风格卷轴背景，营造典雅庄重氛围" class="opacity-40 absolute inset-0 w-full h-full z-0 object-cover object-top animate-scale-in reveal-item" src="https://space.coze.cn/s/X2E_p5psnkY/"/>
        <div class="absolute inset-0 bg-gradient-to-b from-[#0d1a26]/80 via-[#0d1a26]/60 to-[#0d1a26]/90 z-10"></div>
        
        <!-- 装饰元素 -->
        <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[900px] h-[900px] bg-radial from-[#1e90ff]/20 to-transparent rounded-full pointer-events-none z-20"></div>
        <div class="absolute top-10 left-10 w-20 h-20 border-2 border-[#ffd700]/30 rounded-full z-20"></div>
        <div class="absolute bottom-10 right-10 w-32 h-32 border-2 border-[#ff4757]/20 rounded-full z-20"></div>
        
        <div class="relative z-30 flex flex-col items-center justify-center text-center text-white p-12 w-full h-full">
            <header class="flex-shrink-0 mb-12">
                <h1 class="reveal-item text-5xl font-bold tracking-wider text-shadow" style="background: linear-gradient(90deg, #ffffff, #ffd700); -webkit-background-clip: text; -webkit-text-fill-color: transparent; transition: all 0.5s ease;">
                    家国情怀名言警句集锦
                </h1>
                <div class="w-32 h-1 bg-gradient-to-r from-[#ff4757] to-[#1e90ff] mt-4 mx-auto reveal-item"></div>
            </header>
            
            <!-- 名言展示区 -->
            <main class="flex-grow w-full flex items-center justify-center min-h-0 px-8">
                <div class="w-full max-w-6xl grid grid-cols-2 gap-x-16 gap-y-10 text-xl md:text-2xl text-white leading-relaxed text-shadow p-8 rounded-lg">
                    <!-- 名言1 -->
                    <div class="quote-container reveal-item" data-delay="100">
                        <div class="quote-mark left text-[#1e90ff]/20">"</div>
                        <p class="quote-text relative z-10 transition-all 0.3s hover:translate-x-2">
                            苟利国家，不求富贵
                            <span class="audio-play-btn ml-2" data-audio="custom-audio-1">
                                <i class="fas fa-volume-up"></i>
                            </span>
                            <br><span class="text-gray-300 ml-2 text-base mt-2 block">——《礼记》</span>
                        </p>
                        <div class="quote-mark right text-[#1e90ff]/20">"</div>
                    </div>
                    
                    <!-- 名言2 -->
                    <div class="quote-container reveal-item" data-delay="200">
                        <div class="quote-mark left text-[#2ed573]/20">"</div>
                        <p class="quote-text relative z-10 transition-all 0.3s hover:translate-x-2">
                            位卑未敢忘忧国
                            <span class="audio-play-btn ml-2" data-audio="custom-audio-2">
                                <i class="fas fa-volume-up"></i>
                            </span>
                            <br><span class="text-gray-300 ml-2 text-base mt-2 block">——陆游</span>
                        </p>
                        <div class="quote-mark right text-[#2ed573]/20">"</div>
                    </div>
                    
                    <!-- 名言3 -->
                    <div class="quote-container reveal-item" data-delay="300">
                        <div class="quote-mark left text-[#ff4757]/20">"</div>
                        <p class="quote-text relative z-10 transition-all 0.3s hover:translate-x-2">
                            人生自古谁无死，留取丹心照汗青
                            <span class="audio-play-btn ml-2" data-audio="custom-audio-3">
                                <i class="fas fa-volume-up"></i>
                            </span>
                            <br><span class="text-gray-300 ml-2 text-base mt-2 block">——文天祥</span>
                        </p>
                        <div class="quote-mark right text-[#ff4757]/20">"</div>
                    </div>
                    
                    <!-- 名言4 -->
                    <div class="quote-container reveal-item" data-delay="400">
                        <div class="quote-mark left text-[#ffd700]/20">"</div>
                        <p class="quote-text relative z-10 transition-all 0.3s hover:translate-x-2">
                            先天下之忧而忧，后天下之乐而乐
                            <span class="audio-play-btn ml-2" data-audio="custom-audio-4">
                                <i class="fas fa-volume-up"></i>
                            </span>
                            <br><span class="text-gray-300 ml-2 text-base mt-2 block">——范仲淹</span>
                        </p>
                        <div class="quote-mark right text-[#ffd700]/20">"</div>
                 
<!-- 名言5 -->
<div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="实现中华民族伟大复兴，就是中华民族近代以来最伟大的梦想">
    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
    <div class="flex items-start gap-4 relative">
        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
        <div class="flex-1">
            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                实现中华民族伟大复兴，就是中华民族近代以来最伟大的梦想
            </p>
            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——习近平</p>
        </div>
    </div>
    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
            <i class="fa fa-play"></i>
            <span>播放朗读</span>
        </button>
        <div class="audio-indicator hidden">
            <span class="wave-bar h-2"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-4"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-2"></span>
        </div>
    </div>
</div>

<!-- 名言10 -->
<div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="人民对美好生活的向往，就是我们的奋斗目标">
    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
    <div class="flex items-start gap-4 relative">
        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
        <div class="flex-1">
            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                人民对美好生活的向往，就是我们的奋斗目标
            </p>
            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——习近平</p>
        </div>
    </div>
    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
            <i class="fa fa-play"></i>
            <span>播放朗读</span>
        </button>
        <div class="audio-indicator hidden">
            <span class="wave-bar h-2"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-4"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-2"></span>
        </div>
    </div>
</div>

<!-- 名言11 -->
<div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="幸福都是奋斗出来的">
    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
    <div class="flex items-start gap-4 relative">
        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
        <div class="flex-1">
            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                幸福都是奋斗出来的
            </p>
            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——习近平</p>
        </div>
    </div>
    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
            <i class="fa fa-play"></i>
            <span>播放朗读</span>
        </button>
        <div class="audio-indicator hidden">
            <span class="wave-bar h-2"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-4"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-2"></span>
        </div>
    </div>
</div>

<!-- 名言12 -->
<div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="路漫漫其修远兮，吾将上下而求索">
    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
    <div class="flex items-start gap-4 relative">
        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
        <div class="flex-1">
            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                路漫漫其修远兮，吾将上下而求索
            </p>
            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——屈原</p>
        </div>
    </div>
    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
            <i class="fa fa-play"></i>
            <span>播放朗读</span>
        </button>
        <div class="audio-indicator hidden">
            <span class="wave-bar h-2"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-4"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-2"></span>
        </div>
    </div>
</div>

<!-- 名言13 -->
<div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="为中华之崛起而读书">
    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
    <div class="flex items-start gap-4 relative">
        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
        <div class="flex-1">
            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                为中华之崛起而读书
            </p>
            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——周恩来</p>
        </div>
    </div>
    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
            <i class="fa fa-play"></i>
            <span>播放朗读</span>
        </button>
        <div class="audio-indicator hidden">
            <span class="wave-bar h-2"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-4"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-2"></span>
        </div>
    </div>
</div>

<!-- 名言14 -->
<div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="我们爱我们的民族，这是我们自信心的泉源">
    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
    <div class="flex items-start gap-4 relative">
        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
        <div class="flex-1">
            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                我们爱我们的民族，这是我们自信心的泉源
            </p>
            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——周恩来</p>
        </div>
    </div>
    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
            <i class="fa fa-play"></i>
            <span>播放朗读</span>
        </button>
        <div class="audio-indicator hidden">
            <span class="wave-bar h-2"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-4"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-2"></span>
        </div>
    </div>
</div>

<!-- 名言15 -->
<div class="quote-card bg-white/5 rounded-xl p-6 card-hover border border-white/10 relative overflow-hidden group" data-text="新时代属于每一个人，每一个人都是新时代的见证者、开创者、建设者">
    <div class="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
    <div class="flex items-start gap-4 relative">
        <i class="fa fa-quote-left text-primary text-2xl mt-1 transition-colors duration-300 group-hover:text-secondary"></i>
        <div class="flex-1">
            <p class="text-[clamp(1.1rem,2vw,1.3rem)] font-medium leading-relaxed mb-3 transition-colors duration-300 group-hover:text-secondary">
                新时代属于每一个人，每一个人都是新时代的见证者、开创者、建设者
            </p>
            <p class="text-gray-400 text-[clamp(0.9rem,1.5vw,1rem)]">——习近平</p>
        </div>
    </div>
    <div class="mt-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button class="play-btn text-sm bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-full flex items-center gap-2 transition-all">
            <i class="fa fa-play"></i>
            <span>播放朗读</span>
        </button>
        <div class="audio-indicator hidden">
            <span class="wave-bar h-2"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-4"></span>
            <span class="wave-bar h-3"></span>
            <span class="wave-bar h-2"></span>
        </div>
    </div>
</div>
        <!-- 第11页：结语 -->
        <div class="slide" id="slide11" data-reveal-count="5">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <div class="w-full h-1/2 bg-gray-200">
                    <img alt="长城日出，展现壮丽开阔的希望景象" class="w-full h-full object-cover object-top reveal-item" src="https://s.coze.cn/image/GUO4vtAGxy8/"/>
                </div>
                <div class="w-full h-1/2 flex flex-col justify-center p-16">
                    <header class="mb-6">
                        <h1 class="text-4xl font-bold text-[#2d3436] reveal-item">八、结语：让家国情怀照亮前行之路</h1>
                        <div class="w-24 h-1.5 bg-[#1e90ff] mt-4 rounded-full reveal-item"></div>
                    </header>
                    <main>
                        <p class="text-xl text-[#636e72] leading-relaxed reveal-item" data-delay="100">
                            家国情怀是中华民族的精神纽带，是穿越千年的文化基因，更是新时代奋斗者的精神坐标。
                        </p>
                        <p class="text-xl text-[#636e72] leading-relaxed reveal-item" data-delay="200">
                            从<span class="animate-pulse-highlight">土尔扈特东归</span>的民族大义，到<span class="animate-pulse-highlight">"两弹一星"</span>的科技报国；
                        </p>
                        <p class="text-xl text-[#636e72] leading-relaxed reveal-item" data-delay="300">
                            让我们将个人梦想融入国家发展，共书民族复兴壮丽篇章。
                            <span class="audio-play-btn" data-audio="https://example.com/audio/conclusion.mp3">
                                <i class="fas fa-volume-up"></i>
                            </span>
                        </p>
                    </main>
                </div>
                <div class="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] via-[#2ed573] to-[#ff4757] z-20"></div>
            </div>
        </div>

        <!-- 第12页：致谢 -->
        <div class="slide" id="slide12" data-reveal-count="3">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#0d1a26]">
                <img alt="飘扬的五星红旗旁有多只白鸽飞翔，象征国家繁荣与和平发展" class="absolute inset-0 w-full h-full z-0 opacity-40 object-cover object-top animate-scale-in reveal-item" src="https://s.coze.cn/image/Cc0jNPmyM9U/"/>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[900px] h-[900px] bg-radial from-[#2ed573]/10 to-transparent rounded-full pointer-events-none z-10"></div>
                <div class="relative z-20 flex flex-col items-center justify-center text-center text-white p-12">
                    <h1 class="text-6xl font-bold tracking-wider text-shadow reveal-item" style="background: linear-gradient(90deg, #ffffff, #ffd700); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        感谢聆听！
                    </h1>
                    <div class="w-32 h-1 bg-[#2ed573] my-8 reveal-item"></div>
                    <p class="text-2xl font-light tracking-widest text-shadow reveal-item" data-delay="200">
                        <span class="animate-pulse-highlight">愿家国情怀永驻心间，激励我们奋勇向前。</span>
                    </p>
                </div>
                <div class="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-[#ff4757] via-[#1e90ff] to-[#2ed573] z-20"></div>
            </div>
        </div>
    </div>

    <!-- 控制栏 -->
    <div class="control-bar">
        <button class="control-btn" id="prevBtn"><i class="fas fa-chevron-left"></i></button>
        <button class="control-btn" id="playPauseBtn"><i class="fas fa-play"></i></button>
        <button class="control-btn" id="nextBtn"><i class="fas fa-chevron-right"></i></button>
        <button class="control-btn audio-btn" id="stopAudioBtn"><i class="fas fa-volume-mute"></i></button>
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <div class="slide-info" id="slideInfo">第 1 页 / 共 12 页</div>
    </div>

    <script>
        // 全局变量
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        let currentSlide = 0;
        let currentRevealIndex = 0;
        let isPlaying = false;
        let playInterval;
        let audioPlayer = document.getElementById('audioPlayer');
        let currentAudioBtn = null;
        
        // DOM元素
        const slideInfo = document.getElementById('slideInfo');
        const progressBar = document.getElementById('progressBar');
        const progressContainer = document.getElementById('progressContainer');
        const playPauseBtn = document.getElementById('playPauseBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const stopAudioBtn = document.getElementById('stopAudioBtn');

        // 初始化图表
        function initCharts() {
            // 教育成就图表
            const achievementCtx = document.getElementById('achievementChart');
            if (achievementCtx) {
                new Chart(achievementCtx, {
                    type: 'bar',
                    data: {
                        labels: ['学前教育', '义务教育', '高中阶段'],
                        datasets: [
                            {
                                label: '1949年',
                                data: [0.4, 20, 1.1],
                                backgroundColor: 'rgba(255, 99, 132, 0.6)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            },
                            {
                                label: '2023年',
                                data: [91.1, 100, 91.8],
                                backgroundColor: 'rgba(54, 162, 235, 0.6)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'top', labels: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } },
                            title: { display: true, text: '中国教育普及水平变化（1949-2023）', font: { family: "'Noto Sans SC', sans-serif", size: 18, weight: 'bold' }, color: '#2d3436', padding: { bottom: 20 } }
                        },
                        scales: {
                            y: { beginAtZero: true, title: { display: true, text: '毛入学率 (%)', font: { family: "'Noto Sans SC', sans-serif", size: 14 } }, ticks: { font: { family: "'Noto Sans SC', sans-serif" } } },
                            x: { ticks: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } }
                        }
                    }
                });
            }

            // 科技成就图表
            const techCtx = document.getElementById('techAchievementChart');
            if (techCtx) {
                new Chart(techCtx, {
                    type: 'line',
                    data: {
                        labels: ['2015', '2017', '2019', '2021', '2023'],
                        datasets: [
                            {
                                label: '中国研发经费投入（万亿元）',
                                data: [1.4, 1.7, 2.2, 2.8, 3.5],
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                fill: true
                            },
                            {
                                label: '全球创新指数排名',
                                data: [29, 27, 14, 12, 10],
                                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                fill: true
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'top', labels: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } },
                            title: { display: true, text: '中国科技发展成就（2015-2023）', font: { family: "'Noto Sans SC', sans-serif", size: 18, weight: 'bold' }, color: '#2d3436', padding: { bottom: 20 } }
                        },
                        scales: {
                            y: { beginAtZero: true, title: { display: true, text: ['研发经费（万亿元）', '创新指数排名'], font: { family: "'Noto Sans SC', sans-serif", size: 14 } }, ticks: { font: { family: "'Noto Sans SC', sans-serif" } } },
                            x: { ticks: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } }
                        }
                    }
                });
            }
        }

        // 重置当前幻灯片的元素显示状态
        function resetSlideReveal(slideIndex) {
            const slide = slides[slideIndex];
            const revealItems = slide.querySelectorAll('.reveal-item');
            revealItems.forEach(item => {
                item.classList.remove('visible');
            });
        }

        // 显示当前幻灯片的下一个元素
        function revealNextItem() {
            const currentSlideEl = slides[currentSlide];
            const revealItems = currentSlideEl.querySelectorAll('.reveal-item');
            
            // 如果还有未显示的元素，显示下一个
            if (currentRevealIndex < revealItems.length) {
                const item = revealItems[currentRevealIndex];
                const delay = item.dataset.delay || 0;
                
                setTimeout(() => {
                    item.classList.add('visible');
                }, delay);
                
                currentRevealIndex++;
                return true; // 还有元素未显示
            }
            
            return false; // 所有元素都已显示
        }

        // 显示指定幻灯片
        function showSlide(index) {
            // 停止当前音频
            stopAudio();
            
            // 更新上一张和下一张的样式
            slides.forEach((slide, i) => {
                slide.classList.remove('active', 'prev', 'next');
                if (i < index) slide.classList.add('prev');
                else if (i > index) slide.classList.add('next');
            });
            
            // 隐藏所有幻灯片，显示当前幻灯片
            currentSlide = index;
            slides[currentSlide].classList.add('active');
            
            // 重置当前幻灯片的元素显示状态
            resetSlideReveal(currentSlide);
            currentRevealIndex = 0;
            
            // 显示第一个元素
            setTimeout(() => {
                revealNextItem();
            }, 300);
            
            // 更新信息和进度
            updateSlideInfo();
            updateProgress();
            
            // 重新初始化当前页图表
            if (currentSlide === 4 || currentSlide === 8) {
                setTimeout(initCharts, 500);
            }
        }

        // 更新幻灯片信息
        function updateSlideInfo() {
            slideInfo.textContent = `第 ${currentSlide + 1} 页 / 共 ${totalSlides} 页`;
        }

        // 更新进度条
        function updateProgress() {
            const progress = (currentSlide / (totalSlides - 1)) * 100;
            progressBar.style.width = `${progress}%`;
        }

        // 上一页
        function prevSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(currentSlide);
        }

        // 下一页
        function nextSlide() {
            // 先尝试显示当前页的下一个元素
            if (!revealNextItem()) {
                // 如果所有元素都已显示，则切换到下一页
                currentSlide = (currentSlide + 1) % totalSlides;
                showSlide(currentSlide);
            }
        }

        // 播放/暂停
        function togglePlayPause() {
            if (isPlaying) {
                // 暂停
                clearInterval(playInterval);
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            } else {
                // 播放
                playInterval = setInterval(nextSlide, 5000); // 5秒切换一次
                playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            }
            isPlaying = !isPlaying;
        }

        // 播放音频
        function playAudio(audioUrl, btnElement) {
            // 停止当前正在播放的音频
            stopAudio();
            
            // 设置新音频源并播放
            audioPlayer.src = audioUrl;
            audioPlayer.play().then(() => {
                // 更新按钮状态
                btnElement.classList.add('playing');
                btnElement.innerHTML = '<i class="fas fa-volume-up"></i>';
                currentAudioBtn = btnElement;
            }).catch(error => {
                console.error("音频播放失败:", error);
            });
        }

        // 停止音频
        function stopAudio() {
            if (!audioPlayer.paused) {
                audioPlayer.pause();
                audioPlayer.src = '';
            }
            
            // 重置所有音频按钮状态
            if (currentAudioBtn) {
                currentAudioBtn.classList.remove('playing');
                currentAudioBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
                currentAudioBtn = null;
            }
        }

        // 点击进度条跳转
        progressContainer.addEventListener('click', (e) => {
            const rect = progressContainer.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const percent = x / rect.width;
            currentSlide = Math.floor(percent * (totalSlides - 1));
            showSlide(currentSlide);
        });

        // 点击幻灯片空白处显示下一个元素
        document.querySelector('.ppt-container').addEventListener('click', (e) => {
            // 如果点击的不是音频按钮或控制按钮，则显示下一个元素
            if (!e.target.closest('.audio-play-btn') && !e.target.closest('.control-bar')) {
                nextSlide();
            }
        });

        // 音频按钮点击事件
        document.querySelectorAll('.audio-play-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡，避免触发幻灯片点击
                const audioUrl = btn.dataset.audio;
                
                // 如果是当前正在播放的音频，则停止
                if (btn === currentAudioBtn && !audioPlayer.paused) {
                    stopAudio();
                } else {
                    // 否则播放新音频
                    playAudio(audioUrl, btn);
                }
            });
        });

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            // 避免在输入框等元素中触发
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
            
            switch (e.key) {
                case 'ArrowLeft':
                    prevSlide();
                    break;
                case 'ArrowRight':
                case ' ': // 空格键
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'p':
                case 'P':
                    togglePlayPause();
                    break;
                case 'm':
                case 'M':
                    stopAudio();
                    break;
            }
        });

        // 触摸控制（左右滑动）
        let touchStartX = 0;
        let touchStartTime = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartTime = Date.now();
        }, false);

        document.addEventListener('touchend', (e) => {
            if (!e.changedTouches[0]) return;
            
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndTime = Date.now();
            const diffX = touchStartX - touchEndX;
            const diffTime = touchEndTime - touchStartTime;
            
            // 只有滑动距离和时间在合理范围内才视为有效滑动
            if (diffTime < 500) {
                if (diffX > 50) {
                    nextSlide(); // 向右滑动，下一页
                } else if (diffX < -50) {
                    prevSlide(); // 向左滑动，上一页
                }
            }
        }, false);

        // 按钮事件绑定
        prevBtn.addEventListener('click', prevSlide);
        nextBtn.addEventListener('click', nextSlide);
        playPauseBtn.addEventListener('click', togglePlayPause);
        stopAudioBtn.addEventListener('click', stopAudio);

        // 音频结束时重置按钮状态
        audioPlayer.addEventListener('ended', () => {
            if (currentAudioBtn) {
                currentAudioBtn.classList.remove('playing');
                currentAudioBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
                currentAudioBtn = null;
            }
        });

        // 初始化
        window.addEventListener('load', () => {
            // 设置所有幻灯片初始状态
            slides.forEach((slide, i) => {
                if (i !== 0) {
                    slide.classList.add(i < 0 ? 'prev' : 'next');
                }
            });
            
            showSlide(0);
            initCharts();
        });
    </script>
</body>
</html>
