<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>高级随机点名器 · 统一增强版</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <style>
    :root{--bg1:#667eea;--bg2:#764ba2}
    html,body{height:100%}
    body{font-family:'Inter',sans-serif;min-height:100vh;background:linear-gradient(135deg,var(--bg1) 0%,var(--bg2) 100%)}
    .glass{background:rgba(255,255,255,.12);backdrop-filter:blur(10px);border-radius:20px;border:1px solid rgba(255,255,255,.22)}
    .btn{border-radius:9999px;padding:.65rem 1.25rem;font-weight:600;transition:.2s}
    .btn-primary{background:linear-gradient(135deg,var(--bg1),var(--bg2));color:#fff}
    .btn-ghost{background:rgba(255,255,255,.18);color:#fff;border:1px solid rgba(255,255,255,.3)}
    .pill{background:rgba(255,255,255,.18);border:1px solid rgba(255,255,255,.3)}
    .modal{position:fixed;inset:0;background:rgba(0,0,0,.55);display:none;align-items:center;justify-content:center;z-index:50}
    .modal.show{display:flex}
    .modal-card{max-width:95vw;max-height:85vh;overflow:auto}
    .draggable-item{padding:.5rem 1rem;background:rgba(255,255,255,.1);border-radius:.5rem;margin-bottom:.5rem;cursor:grab}
    .draggable-item.dragging{background:rgba(255,255,255,.25)}
  </style>
</head>
<body class="text-white">
  <div class="container mx-auto px-4 py-8 max-w-7xl">
    <header class="text-center mb-8">
      <h1 class="text-4xl md:text-5xl font-extrabold drop-shadow-sm">高级随机点名器 · 统一增强版</h1>
      <p class="text-white/80 mt-2">支持批量导入、多列识别、公平算法与高级功能。</p>
    </header>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <section class="lg:col-span-2 space-y-6">
        <div class="glass p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold">📁 名单导入</h2>
            <div class="flex gap-2">
              <button id="btnCustomTemplate" class="btn btn-ghost text-sm">自定义模板</button>
              <button id="btnClearAll" class="btn btn-ghost text-sm">清空名单</button>
            </div>
          </div>
          <div class="flex flex-wrap gap-3">
            <input id="fileInput" class="hidden" type="file" accept=".txt,.csv,.xls,.xlsx" multiple>
            <button class="btn btn-ghost" onclick="document.getElementById('fileInput').click()">选择文件</button>
            <button id="btnPaste" class="btn btn-ghost">📋 粘贴导入</button>
            <label class="flex items-center gap-2 text-sm text-white/90 pill px-3 py-2 rounded-full">
              <input id="hasHeader" type="checkbox" class="scale-110" checked>
              <span>首行为表头</span>
            </label>
          </div>
          <p id="importStatus" class="text-white/80 text-sm mt-3"></p>
        </div>

        <div class="glass p-6">
          <h2 class="text-xl font-semibold mb-4">✏️ 快速添加</h2>
          <div class="flex gap-3">
            <input id="nameInput" type="text" placeholder="输入姓名（支持批量，张三|一班）" class="flex-1 px-4 py-3 rounded-full bg-white/10 text-white border border-white/20 focus:outline-none">
            <button id="btnAdd" class="btn btn-primary">添加</button>
          </div>
        </div>

        <div class="glass p-8">
          <div id="display" class="min-h-[180px] flex items-center justify-center text-center">
            <p class="text-white/70">点击下方按钮开始点名…</p>
          </div>
          <div class="mt-6 flex flex-wrap justify-center gap-3">
            <button id="btnDraw" class="btn btn-primary text-lg px-8 py-4">🎯 开始点名</button>
            <button id="btnMulti" class="btn btn-ghost">批量抽取</button>
            <button id="btnFullscreen" class="btn btn-ghost">全屏显示</button>
            <button id="btnAutoCycle" class="btn btn-ghost">循环点名</button>
            <button id="btnAdvanced" class="btn btn-ghost">高级设置</button>
          </div>
        </div>
      </section>

      <aside class="space-y-6">
        <div class="glass p-6">
          <div class="flex items-center justify-between mb-3">
            <h2 class="text-xl font-semibold">👥 名单管理</h2>
            <span class="pill px-3 py-1 rounded-full text-sm">共 <b id="countAll">0</b> 人</span>
          </div>
          <div id="list" class="max-h-[320px] overflow-y-auto space-y-2 pr-1">
            <p class="text-white/60 text-center py-4">暂无数据</p>
          </div>
        </div>
      </aside>
    </div>
  </div>

  <div id="modalTemplate" class="modal">
    <div class="modal-card glass p-6 rounded-2xl w-[min(800px,95vw)] text-white">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-2xl font-bold">自定义模板设置</h3>
        <button class="btn btn-ghost" onclick="closeModal('modalTemplate')">关闭</button>
      </div>
      <p class="mb-4 text-white/70 text-sm">拖拽排序或勾选需要的列，用于指定姓名和标签列。</p>
      <div id="templateColumnList" class="bg-white/10 rounded-xl p-3 max-h-[45vh] overflow-auto"></div>
      <div class="mt-4 flex justify-end gap-2">
        <button class="btn btn-ghost" onclick="closeModal('modalTemplate')">取消</button>
        <button id="btnSaveTemplate" class="btn btn-primary">保存模板</button>
      </div>
    </div>
  </div>

  <script>
    let people=[];

    function closeModal(id){document.getElementById(id).classList.remove('show');}

    function renderList(){
      const list=document.getElementById('list');
      list.innerHTML='';
      if(people.length===0){list.innerHTML='<p class="text-white/60 text-center py-4">暂无数据</p>';} else {
        people.forEach((p,i)=>{
          const div=document.createElement('div');
          div.className='pill px-3 py-2 flex justify-between';
          div.innerHTML=`<span>${p.name}${p.tag?' | '+p.tag:''}</span><button onclick="removePerson(${i})">❌</button>`;
          list.appendChild(div);
        });
      }
      document.getElementById('countAll').textContent=people.length;
    }

    function removePerson(i){people.splice(i,1);renderList();}

    // 文件导入逻辑
    document.getElementById('fileInput').addEventListener('change',e=>{
      const file=e.target.files[0];
      if(!file)return;
      const reader=new FileReader();
      if(file.name.endsWith('.txt')){
        reader.onload=()=>{
          const lines=reader.result.split(/\r?\n/).filter(l=>l.trim());
          lines.forEach(line=>{
            const [name,tag]=line.split('|');
            people.push({name:name.trim(),tag:tag?tag.trim():''});
          });
          renderList();
        };
        reader.readAsText(file,'utf-8');
      } else if(file.name.endsWith('.csv')){
        reader.onload=()=>{
          Papa.parse(reader.result,{
            header:document.getElementById('hasHeader').checked,
            complete:res=>{
              const data=res.data;
              data.forEach(row=>{
                if(typeof row==='object'){
                  const values=Object.values(row).filter(v=>v);
                  if(values.length>0)people.push({name:values[0],tag:values[1]||''});
                } else if(Array.isArray(row)){
                  if(row.length>0)people.push({name:row[0],tag:row[1]||''});
                }
              });
              renderList();
            }
          });
        };
        reader.readAsText(file,'utf-8');
      } else if(file.name.endsWith('.xls') || file.name.endsWith('.xlsx')){
        reader.onload=(evt)=>{
          const data=new Uint8Array(evt.target.result);
          const workbook=XLSX.read(data,{type:'array'});
          const sheet=workbook.Sheets[workbook.SheetNames[0]];
          const json=XLSX.utils.sheet_to_json(sheet,{header:1});
          json.forEach((row,i)=>{
            if(i===0 && document.getElementById('hasHeader').checked) return;
            if(row.length>0) people.push({name:row[0],tag:row[1]||''});
          });
          renderList();
        };
        reader.readAsArrayBuffer(file);
      }
    });

    // 快速添加
    document.getElementById('btnAdd').addEventListener('click',()=>{
      const val=document.getElementById('nameInput').value.trim();
      if(!val)return;
      val.split(/[,
;]/).forEach(s=>{
        const [name,tag]=s.split('|');
        if(name.trim())people.push({name:name.trim(),tag:tag?tag.trim():''});
      });
      document.getElementById('nameInput').value='';
      renderList();
    });

    // 清空
    document.getElementById('btnClearAll').addEventListener('click',()=>{
      people=[];renderList();
    });

    // 粘贴导入
    document.getElementById('btnPaste').addEventListener('click',()=>{
      const text=prompt('请粘贴名单，每行一个姓名，可加标签：张三|一班');
      if(text){
        text.split(/\r?\n/).forEach(line=>{
          const [name,tag]=line.split('|');
          if(name.trim())people.push({name:name.trim(),tag:tag?tag.trim():''});
        });
        renderList();
      }
    });

    // 模板设置
    document.getElementById('btnCustomTemplate').addEventListener('click',()=>{
      const sampleCols=['学号','姓名','班级','备注'];
      const list=document.getElementById('templateColumnList');
      list.innerHTML='';
      sampleCols.forEach(col=>{
        const item=document.createElement('div');
        item.className='draggable-item flex items-center justify-between';
        item.innerHTML=`<span>${col}</span>
          <div class='flex gap-2'>
            <label><input type='radio' name='nameCol' value='${col}'> 姓名</label>
            <label><input type='radio' name='tagCol' value='${col}'> 标签</label>
          </div>`;
        list.appendChild(item);
      });
      Sortable.create(list,{animation:150});
      document.getElementById('modalTemplate').classList.add('show');
    });

    document.getElementById('btnSaveTemplate').addEventListener('click',()=>{
      const nameCol=document.querySelector("input[name='nameCol']:checked");
      if(!nameCol){alert('请选择姓名列');return;}
      const tagCol=document.querySelector("input[name='tagCol']:checked");
      const order=[...document.querySelectorAll('#templateColumnList .draggable-item span')].map(el=>el.textContent);
      const tpl={name:nameCol.value,tag:tagCol?tagCol.value:'',order};
      localStorage.setItem('customTemplate',JSON.stringify(tpl));
      alert('模板已保存！姓名列：'+tpl.name+(tpl.tag?('，标签列：'+tpl.tag):''));
      closeModal('modalTemplate');
    });

    renderList();
  </script>
</body>
</html>
