<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=1280, initial-scale=1.0" name="viewport"/>
    <title>家国情怀：从历史传承到时代担当 - PPT播放器</title>
    <script src="https://unpkg.byted-static.com/coze/space_ppt_lib/1.0.3-alpha.12/lib/tailwindcss.js"></script>
    <script src="https://unpkg.byted-static.com/chart.js/4.5.0/dist/chart.umd.js"></script>
    <script src="https://unpkg.byted-static.com/fortawesome/fontawesome-free/6.7.2/js/all.min.js" data-auto-replace-svg="nest"></script>
    <link href="https://lf-code-agent.coze.cn/obj/x-ai-cn/fonts/google/google-all-fonts.css" rel="stylesheet"/>
    <style>
        .font-noto { font-family: 'Noto Sans SC', sans-serif; }
        body { margin: 0; padding: 0; overflow: hidden; background: #000; }
        /* PPT容器样式 */
        .ppt-container { 
            width: 100vw; 
            height: 100vh; 
            position: relative; 
        }
        /* 单页幻灯片样式 */
        .slide { 
            width: 100%; 
            height: 100%; 
            position: absolute; 
            top: 0; 
            left: 0; 
            opacity: 0; 
            transition: opacity 0.8s ease-in-out; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
        }
        .slide.active { opacity: 1; z-index: 10; }
        /* 控制栏样式 */
        .control-bar {
            position: fixed;
            bottom: 20px;
            left: 0;
            right: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            padding: 10px 20px;
            background: rgba(0,0,0,0.7);
            border-radius: 8px;
            max-width: 800px;
            margin: 0 auto;
        }
        .control-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #1e90ff;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        .control-btn:hover {
            background: #0b6bcb;
            transform: scale(1.1);
        }
        .progress-container {
            flex: 1;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            overflow: hidden;
            cursor: pointer;
        }
        .progress-bar {
            height: 100%;
            background: #1e90ff;
            width: 0%;
            transition: width 0.3s ease;
        }
        .slide-info {
            color: white;
            font-size: 14px;
            font-family: 'Noto Sans SC', sans-serif;
        }
        /* 通用动画样式 */
        @keyframes fadeInOut { 0% { opacity: 0; } 100% { opacity: 1; } }
        @keyframes slideUpFade { 0% { transform: translateY(50px); opacity: 0; } 100% { transform: translateY(0); opacity: 1; } }
        @keyframes fadeIn { 0% { opacity: 0; } 100% { opacity: 1; } }
        @keyframes scaleFade { 0% { transform: scale(0.8); opacity: 0; } 100% { transform: scale(1); opacity: 1; } }
        @keyframes highlightPulse { 0% { background-color: rgba(255, 215, 0, 0.3); transform: scale(1); } 50% { background-color: rgba(255, 215, 0, 0.6); transform: scale(1.02); } 100% { background-color: rgba(255, 215, 0, 0.3); transform: scale(1); } }
        @keyframes underlineBlink { 0% { text-decoration: underline; opacity: 1; } 50% { text-decoration: none; opacity: 0.5; } 100% { text-decoration: underline; opacity: 1; } }
        @keyframes blink { 0%, 100% { opacity: 1; } 50% { opacity: 0.4; } }
        .animate-fade-in-out { animation: fadeInOut 0.5s ease-in-out; }
        .animate-slide-up { animation: slideUpFade 0.6s ease-out forwards; }
        .animate-fade { animation: fadeIn 0.6s ease-out forwards; }
        .animate-scale-fade { animation: scaleFade 0.8s ease-out forwards; }
        .animate-highlight { animation: highlightPulse 2s infinite; padding: 0 4px; border-radius: 2px; }
        .animate-underline-blink { animation: underlineBlink 1.5s infinite; }
        .text-shadow-custom { text-shadow: 0 2px 10px rgba(0, 0, 0, 0.6); }
        .bg-radial-gradient { background-image: radial-gradient(circle, var(--tw-gradient-from), var(--tw-gradient-to)); }
        .highlight-quote { background-color: #fff3cd; padding: 0 4px; border-radius: 2px; transition: transform 0.3s ease; }
        .highlight-quote:hover { transform: scale(1.02); }
        .blinking-underline { position: relative; }
        .blinking-underline::after { content: ''; position: absolute; width: 100%; height: 2px; bottom: -2px; left: 0; background-color: #1e90ff; animation: blink 1.5s infinite; }
        .data-highlight { background-color: #fff3cd; padding: 0 4px; border-radius: 3px; transition: transform 0.3s ease; }
        .data-highlight:hover { transform: scale(1.05); }
    </style>
</head>
<body class="font-noto">
    <!-- PPT容器 -->
    <div class="ppt-container" id="pptContainer">
        <!-- 第1页：封面 -->
        <div class="slide active" id="slide1">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#0d1a26] animate-fade-in-out">
                <img alt="蜿蜒于山峦间的长城剪影，展现历史厚重感" class="absolute inset-0 w-full h-full z-0 opacity-40 object-cover object-top animate-scale-fade" src="https://s.coze.cn/image/mRjvTG5ZzMM/"/>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[900px] h-[900px] bg-radial-gradient from-[#1e90ff]/10 to-transparent rounded-full pointer-events-none z-10"></div>
                <div class="relative z-20 flex flex-col items-center justify-center text-center text-white p-12">
                    <h1 class="text-6xl font-black tracking-wider text-shadow-custom opacity-0 animate-slide-up" style="background: linear-gradient(90deg, #ffffff, #ffd700); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        家国情怀：从历史传承到时代担当
                    </h1>
                    <div class="w-40 h-1 bg-[#ff4757] my-8 rounded-full"></div>
                    <p class="text-2xl font-light tracking-widest text-shadow-custom max-w-4xl animate-fade opacity-0" style="animation-delay: 0.1s;">
                        爱国主义精神、民族自豪感与社会责任感的当代诠释
                    </p>
                </div>
                <div class="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-[#2ed573] via-[#1e90ff] to-[#ff4757] z-20"></div>
            </div>
        </div>

        <!-- 第2页：目录 -->
        <div class="slide" id="slide2">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa] p-16">
                <div class="absolute top-0 right-0 w-1/2 h-full pointer-events-none z-0">
                    <svg fill="none" height="100%" viewbox="0 0 640 720" width="100%" xmlns="http://www.w3.org/2000/svg">
                        <path d="M640 0L400 720" stroke="#1e90ff" stroke-opacity="0.1" stroke-width="2"></path>
                        <path d="M550 0L350 720" stroke="#ff4757" stroke-opacity="0.1" stroke-width="2"></path>
                        <path d="M600 0L450 720" stroke="#2ed573" stroke-opacity="0.1" stroke-width="2"></path>
                    </svg>
                </div>
                <header class="flex-shrink-0 w-full mb-12 z-10">
                    <h1 class="text-5xl font-bold text-[#2d3436] animate-slide-up">目录</h1>
                    <div class="w-24 h-1.5 bg-[#1e90ff] mt-3 rounded-full"></div>
                </header>
                <main class="flex-grow w-full flex items-center min-h-0 z-10">
                    <div class="w-full">
                        <ul class="toc-list columns-2 gap-x-20 text-2xl text-[#2d3436] leading-[2.5]">
                            <li class="flex items-center break-inside-avoid mb-4 animate-fade" style="animation-delay: 0.1s">家国情怀的内涵解析</li>
                            <li class="flex items-center break-inside-avoid mb-4 animate-fade" style="animation-delay: 0.2s">历史长河中的家国记忆</li>
                            <li class="flex items-center break-inside-avoid mb-4 animate-fade" style="animation-delay: 0.3s">民族复兴的辉煌成就</li>
                            <li class="flex items-center break-inside-avoid mb-4 animate-fade blinking-underline" style="animation-delay: 0.4s">榜样力量：家国担当的践行者</li>
                            <li class="flex items-center break-inside-avoid mb-4 animate-fade" style="animation-delay: 0.5s">家国情怀的教育实践</li>
                            <li class="flex items-center break-inside-avoid mb-4 animate-fade" style="animation-delay: 0.6s">新时代的家国责任与行动路径</li>
                            <li class="flex items-center break-inside-avoid mb-4 animate-fade" style="animation-delay: 0.7s">结语：让家国情怀照亮前行之路</li>
                        </ul>
                    </div>
                </main>
            </div>
        </div>

        <!-- 第3页：家国情怀的内涵解析 -->
        <div class="slide" id="slide3">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa] p-16">
                <div class="absolute bottom-8 right-8 text-[120px] font-black text-[#1e90ff]/5 select-none pointer-events-none -rotate-12 z-0 animate-scale-fade">
                    <i class="fas fa-landmark"></i>
                </div>
                <header class="flex-shrink-0 w-full mb-10 z-10">
                    <h1 class="text-4xl font-bold text-[#2d3436] animate-slide-up">一、家国情怀的内涵解析</h1>
                    <div class="w-24 h-1.5 bg-[#1e90ff] mt-4 rounded-full"></div>
                </header>
                <main class="flex-grow w-full flex flex-col justify-center min-h-0 z-10">
                    <div class="space-y-8">
                        <div class="flex items-start gap-6 animate-fade" style="animation-delay: 0.1s;">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#1e90ff]/10 text-[#1e90ff] text-2xl rounded-full flex-shrink-0 mt-1 animate-scale-fade">
                                <i class="fas fa-book-open"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#1e90ff]">核心定义：</strong>个人对家庭和国家共同体的认同与热爱，是爱国主义精神的伦理基础和情感状态，体现为“在家尽孝，为国尽忠”，实践途径是“修己安人，经邦济世”，价值理想是“以身报国，建功立业”。
                            </div>
                        </div>
                        <div class="flex items-start gap-6 animate-fade" style="animation-delay: 0.2s;">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#2ed573]/10 text-[#2ed573] text-2xl rounded-full flex-shrink-0 mt-1 animate-scale-fade">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#2d3436]">历史渊源：</strong>根植于中国氏族血缘宗法制，《礼记》<span class="blinking-underline">“五止十义”</span>奠定伦理与政治秩序统一的基础。
                            </div>
                        </div>
                        <div class="flex items-start gap-6 animate-fade" style="animation-delay: 0.3s;">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#ff4757]/10 text-[#ff4757] text-2xl rounded-full flex-shrink-0 mt-1 animate-scale-fade">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#ff4757]">现代升华：</strong>习近平总书记指出<span class="highlight-quote">“爱国是本分，也是职责”</span>，强调<span class="highlight-quote">“把小我融入大我”</span>，将个人奋斗与国家命运紧密结合。
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- 第4页：家国情怀的传统内核与近代转型 -->
        <div class="slide" id="slide4">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-6 animate-slide-up">家国情怀的传统内核与近代转型</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.1s;">
                                <i class="fas fa-landmark text-[#1e90ff] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p>
                                    <strong class="font-bold text-[#1e90ff]">传统内涵</strong>：以“家国同构”为基础，“天下一体”为逻辑，“仁、义”为核心；孝悌之仁凝练主体，不忍之仁筑牢底线，安人之仁彰显取向，友道之仁丰富选择；忠孝一体为价值凝练，追求“天下太平”。
                                </p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.2s;">
                                <i class="fas fa-history text-[#ff4757] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p>
                                    <strong class="font-bold text-[#ff4757]">近代转型</strong>：与民族危机、救亡图存结合，从“天下一体”反思转向民族国家观念建构，“忧患意识”推动对进步、科学、民主的关注，爱国主义成为近代表达。
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 h-full bg-gray-200">
                        <img alt="近代救亡图存浮雕群像，展现近代家国情怀实践场景" class="w-full h-full object-cover object-top animate-scale-fade" src="https://s.coze.cn/image/Rc08Wze7ztQ/"/>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第5页：历史长河中的家国记忆 -->
        <div class="slide" id="slide5">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#ff4757] to-[#1e90ff]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 h-full bg-gray-200">
                        <img alt="抗美援朝志愿军雪地场景，展现艰苦环境下的钢铁意志" class="w-full h-full object-cover object-top animate-scale-fade" src="https://s.coze.cn/image/5C0Vxi3Mdbs/"/>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-6 animate-slide-up">二、历史长河中的家国记忆</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.1s;">
                                <div class="w-10 h-10 flex items-center justify-center bg-[#1e90ff] text-white rounded-lg flex-shrink-0 mt-1">
                                    <i class="fas fa-landmark"></i>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">古代融合与认同</h2>
                                    <p class="text-[#636e72]">
                                        《礼记》“五止十义”、王昭君与文成公主和亲、“凉州会盟”、土尔扈特东归
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.2s;">
                                <div class="w-10 h-10 flex items-center justify-center bg-[#ff4757] text-white rounded-lg flex-shrink-0 mt-1">
                                    <i class="fas fa-flag"></i>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">近代救亡与抗争</h2>
                                    <p class="text-[#636e72]">
                                        仰韶遗址发现、抗美援朝（<span class="highlight-quote">宋阿毛绝笔</span>）、香港海防历史
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第6页：民族复兴的辉煌成就 -->
        <div class="slide" id="slide6">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full p-12">
                    <div class="w-1/2 flex flex-col justify-center pr-10">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-8 animate-slide-up">三、民族复兴的辉煌成就</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.1s;">
                                <i class="fas fa-chart-line text-[#1e90ff] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#1e90ff] blinking-underline">经济腾飞</strong>：2023年GDP达<strong class="font-bold data-highlight">126万亿元</strong>，较1952年增长<strong class="font-bold data-highlight">223倍</strong>，年均增长<strong class="font-bold data-highlight">7.9%</strong>；人均GDP<strong class="font-bold data-highlight">8.9万元</strong>。</p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.2s;">
                                <i class="fas fa-heart-pulse text-[#2ed573] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#2ed573] blinking-underline">民生改善</strong>：农村贫困人口全部脱贫，人均预期寿命提高<strong class="font-bold data-highlight">43.6岁</strong>，城镇化率达<strong class="font-bold data-highlight">66.1%</strong>。</p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.3s;">
                                <i class="fas fa-satellite-dish text-[#ff4757] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#ff4757] blinking-underline">科技强国</strong>：“两弹一星”精神激励下，1964年原子弹、1967年氢弹、1970年人造卫星发射成功。</p>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center pl-10">
                        <div class="flex-grow relative bg-white rounded-lg shadow-lg p-6 min-h-0 animate-scale-fade">
                            <canvas id="achievementChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第7页：榜样力量：家国担当的践行者 -->
        <div class="slide" id="slide7">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#ff4757] to-[#1e90ff]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 h-full bg-gray-200 flex items-center justify-center overflow-hidden">
                        <img alt="陈祥榕戍边英雄军装照，展现军人庄重形象" class="w-full h-full object-cover object-top transition-transform duration-300 hover:scale-108 hover:ring-4 hover:ring-[#ffd700] animate-scale-fade" src="https://s.coze.cn/image/oaHiEqFHy4s/"/>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-6 animate-slide-up">四、榜样力量：<br/>家国担当的践行者</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.1s;">
                                <i class="fas fa-award text-[#ff4757] mt-1.5 flex-shrink-0"></i>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">英雄烈士</h2>
                                    <p class="text-[#636e72]">陈祥榕（<span class="bg-yellow-200 px-1 rounded hover:scale-105 transition-transform">"清澈的爱，只为中国"</span>）、<span class="blinking-underline">黄令仪</span>（芯片研发）、<span class="blinking-underline">黄宗德</span>（战斗英雄）、<span class="blinking-underline">巴依卡</span>（边防"活地图"）。</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.2s;">
                                <i class="fas fa-star text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <div>
                                    <h2 class="text-xl font-bold text-[#2d3436] mb-2">时代楷模</h2>
                                    <p class="text-[#636e72]"><span class="blinking-underline">郝津芳</span>（带动药农增收）、<span class="blinking-underline">苏莉</span>（拾金不昧）、<span class="blinking-underline">许振超</span>（"振超效率"）。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第8页：企业担当：社会责任与家国情怀 -->
        <div class="slide" id="slide8">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-6 animate-slide-up">企业担当：社会责任与家国情怀</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.1s">
                                <i class="fas fa-hand-holding-heart text-[#1e90ff] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p><strong class="font-bold text-[#1e90ff] blinking-underline">玫德集团</strong>：数字化转型，公益捐资<span class="data-highlight">超5亿元</span>，"玛钢助学基金"助<span class="data-highlight">1万余名</span>学子。</p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.2s">
                                <i class="fas fa-medal text-[#1e90ff] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p><strong class="font-bold text-[#1e90ff] blinking-underline">泰山体育</strong>：服务<span class="data-highlight">8届奥运会</span>，捐赠<span class="data-highlight">1.5亿元</span>乡村体育设施，年产<span class="data-highlight">200万套</span>家庭智能健身包。</p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.3s">
                                <i class="fas fa-leaf text-[#1e90ff] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p><strong class="font-bold text-[#1e90ff] blinking-underline">格创东智</strong>：为<span class="data-highlight">100余家</span>工厂提供能碳管理方案，累计减碳<span class="data-highlight">35万吨</span>，创造岗位<span class="data-highlight">8000余个</span>。</p>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 h-full bg-gray-200">
                        <img alt="智能工厂生产线，展现现代化工业场景" class="w-full h-full object-cover object-top transition-all duration-300 hover:scale-108 hover:ring-2 hover:ring-[#1e90ff] rounded-sm animate-scale-fade" src="https://space.coze.cn/s/dKlD9Mr6W40/"/>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第9页：家国情怀的教育实践 -->
        <div class="slide" id="slide9">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 h-full bg-gray-200">
                        <img alt="高校国防后备连军训队列，展现“行进式”户外实践" class="w-full h-full object-cover object-top animate-scale-fade" src="https://s.coze.cn/image/DAV-MDYxA2o/"/>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-4 animate-slide-up">五、家国情怀的教育实践</h1>
                        <div class="w-20 h-1 bg-[#1e90ff] mb-8 rounded-full"></div>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.1s;">
                                <i class="fas fa-university text-[#1e90ff] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p><strong class="font-bold text-[#2d3436]">高校实践</strong>：<span class="blinking-underline">河北金融学院</span>“四大课堂”、<span class="blinking-underline">南开大学</span>“爱国三问”。</p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.2s;">
                                <i class="fas fa-school text-[#1e90ff] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p><strong class="font-bold text-[#2d3436]">中小学教育</strong>：<span class="blinking-underline">红色地标打卡计划</span>、<span class="blinking-underline">宁波初中</span>“情理共生”思政课。</p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.3s;">
                                <i class="fas fa-handshake text-[#1e90ff] mt-1.5 flex-shrink-0 text-xl"></i>
                                <p><strong class="font-bold text-[#2d3436]">社会教育</strong>：<span class="blinking-underline">“银龄教师”援疆</span>、<span class="blinking-underline">企业助学</span>（如<span class="blinking-underline">玫德集团</span>）。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第10页：传统节日中的家国情怀（新增） -->
        <div class="slide" id="slide10">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa] p-16">
                <div class="absolute bottom-8 right-8 text-[120px] font-black text-[#1e90ff]/5 select-none pointer-events-none -rotate-12 z-0 animate-scale-fade">
                    <i class="fas fa-pagelines"></i>
                </div>
                <header class="flex-shrink-0 w-full mb-10 z-10">
                    <h1 class="text-4xl font-bold text-[#2d3436] animate-slide-up">传统节日中的家国情怀</h1>
                    <div class="w-24 h-1.5 bg-[#1e90ff] mt-4 rounded-full"></div>
                </header>
                <main class="flex-grow w-full flex flex-col justify-center min-h-0 z-10">
                    <div class="space-y-8">
                        <div class="flex items-start gap-6 animate-fade" style="animation-delay: 0.1s;">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#1e90ff]/10 text-[#1e90ff] text-2xl rounded-full flex-shrink-0 mt-1 animate-scale-fade">
                                <i class="fas fa-moon"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#1e90ff]">春节</strong>：中华民族最隆重的传统节日，承载辞旧迎新愿望，蕴含家庭团聚伦理观念，贴春联、挂灯笼等习俗是家国同庆的生动体现。
                            </div>
                        </div>
                        <div class="flex items-start gap-6 animate-fade" style="animation-delay: 0.2s;">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#2ed573]/10 text-[#2ed573] text-2xl rounded-full flex-shrink-0 mt-1 animate-scale-fade">
                                <i class="fas fa-flag"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#2ed573]">端午节</strong>：起源于纪念爱国诗人屈原，赛龙舟、吃粽子等习俗展现团结协作民族精神，传递“长太息以掩涕兮，哀民生之多艰”的家国情怀。
                            </div>
                        </div>
                        <div class="flex items-start gap-6 animate-fade" style="animation-delay: 0.3s;">
                            <div class="w-12 h-12 flex items-center justify-center bg-[#ff4757]/10 text-[#ff4757] text-2xl rounded-full flex-shrink-0 mt-1 animate-scale-fade">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="text-xl text-[#2d3436] leading-relaxed">
                                <strong class="font-bold text-[#ff4757]">中秋节</strong>：以月之圆兆人之团圆，寄托对家人思念和国家统一渴望，赏月、吃月饼习俗体现“家国同构”理念，是民族凝聚力象征。
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- 第11页：科技领域的家国情怀（新增） -->
        <div class="slide" id="slide11">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full p-12">
                    <div class="w-1/2 flex flex-col justify-center pr-10">
                        <h1 class="text-4xl font-bold text-[#2d3436] mb-8 animate-slide-up">科技领域的家国情怀</h1>
                        <div class="space-y-8 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.1s;">
                                <i class="fas fa-microchip text-[#1e90ff] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#1e90ff] blinking-underline">两弹一星元勋</strong>：钱学森、邓稼先等放弃国外优越条件，回国投身国防科技，用<span class="data-highlight">“干惊天动地事，做隐姓埋名人”</span>精神奠定国家国防安全基础。</p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.2s;">
                                <i class="fas fa-rocket text-[#2ed573] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#2ed573] blinking-underline">航天人</strong>：从“东方红一号”到“嫦娥探月”“天问火星”，航天人秉持<span class="data-highlight">“特别能吃苦、特别能战斗”</span>精神，刷新中国高度。</p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.3s;">
                                <i class="fas fa-flask text-[#ff4757] text-2xl mt-1 flex-shrink-0"></i>
                                <p><strong class="font-bold text-[#ff4757] blinking-underline">抗疫科技工作者</strong>：快速研发检测试剂、疫苗，用大数据追踪疫情，为疫情防控提供科技支撑。</p>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 flex flex-col justify-center pl-10">
                        <div class="flex-grow relative bg-white rounded-lg shadow-lg p-6 min-h-0 animate-scale-fade">
                            <canvas id="techAchievementChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第12页：新时代的家国责任与行动路径 -->
        <div class="slide" id="slide12">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <header class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] to-[#2ed573]"></header>
                <div class="flex flex-row w-full h-full">
                    <div class="w-1/2 flex flex-col justify-center p-16">
                        <h1 class="animate-slide-up text-4xl font-bold text-[#2d3436] mb-8">六、新时代的家国责任与行动路径</h1>
                        <div class="space-y-6 text-lg text-[#2d3436] leading-relaxed">
                            <div class="flex items-start gap-4">
                                <i class="fas fa-user-check text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <p class="animate-fade" style="animation-delay: 0.1s"><strong class="font-bold text-[#1e90ff]">个人层面</strong>：<span class="animate-highlight">修己安人、立足岗位、践行公德</span>。</p>
                            </div>
                            <div class="flex items-start gap-4">
                                <i class="fas fa-building text-[#2ed573] mt-1.5 flex-shrink-0"></i>
                                <p class="animate-fade" style="animation-delay: 0.2s"><strong class="font-bold text-[#2ed573]">企业层面</strong>：<span class="animate-highlight">技术创新、公益实践</span>。</p>
                            </div>
                            <div class="flex items-start gap-4">
                                <i class="fas fa-users text-[#ff4757] mt-1.5 flex-shrink-0"></i>
                                <p class="animate-fade" style="animation-delay: 0.3s"><strong class="font-bold text-[#ff4757]">社会层面</strong>：弘扬<span class="animate-underline-blink">劳模精神</span>、<span class="animate-underline-blink">工匠精神</span>，加强爱国主义教育。</p>
                            </div>
                            <div class="flex items-start gap-4">
                                <i class="fas fa-landmark text-[#ffd700] mt-1.5 flex-shrink-0"></i>
                                <p class="animate-fade" style="animation-delay: 0.4s"><strong class="font-bold text-[#ffd700]">国家层面</strong>：推进教育公平，坚持<span class="animate-highlight">科技自立自强</span>。</p>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 h-full bg-gray-200 flex items-center justify-center">
                        <img alt="工匠工作场景，展现精益求精的工匠精神" class="animate-scale-fade w-full h-full object-cover object-top" src="https://s.coze.cn/image/kfu6yN-SfSM/"/>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第13页：家国情怀名言警句集锦 -->
        <div class="slide" id="slide13">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#0d1a26]">
                <img alt="水墨风格卷轴背景，营造典雅庄重氛围" class="opacity-50 absolute inset-0 w-full h-full z-0 object-cover object-top animate-scale-fade" src="https://space.coze.cn/s/X2E_p5psnkY/"/>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[900px] h-[900px] bg-radial-gradient from-[#1e90ff]/10 to-transparent rounded-full pointer-events-none z-10"></div>
                <div class="relative z-20 flex flex-col items-center justify-center text-center text-white p-12 w-full h-full">
                    <header class="flex-shrink-0 mb-8">
                        <h1 class="animate-slide-up text-5xl font-bold tracking-wider text-shadow-custom" style="background: linear-gradient(90deg, #ffffff, #ffd700); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                            家国情怀名言警句集锦
                        </h1>
                        <div class="w-32 h-1 bg-[#ff4757] mt-4 mx-auto rounded-full"></div>
                    </header>
                    <main class="flex-grow w-full flex items-center justify-center min-h-0">
                        <div class="w-full max-w-6xl grid grid-cols-2 gap-x-16 gap-y-6 text-2xl text-white leading-relaxed text-shadow-custom bg-black/40 p-8 rounded-lg">
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.1s">
                                <i class="fas fa-quote-left text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <p class="highlight-quote">“苟利国家，不求富贵”<span class="text-gray-400 ml-2 text-lg blinking-underline">——《礼记》</span></p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.2s">
                                <i class="fas fa-quote-left text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <p class="highlight-quote">“位卑未敢忘忧国”<span class="text-gray-400 ml-2 text-lg blinking-underline">——陆游</span></p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.3s">
                                <i class="fas fa-quote-left text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <p class="highlight-quote">“人生自古谁无死，留取丹心照汗青”<span class="text-gray-400 ml-2 text-lg blinking-underline">——文天祥</span></p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.4s">
                                <i class="fas fa-quote-left text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <p class="highlight-quote">“先天下之忧而忧，后天下之乐而乐”<span class="text-gray-400 ml-2 text-lg blinking-underline">——范仲淹</span></p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.5s">
                                <i class="fas fa-quote-left text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <p class="highlight-quote">“天下兴亡，匹夫有责”<span class="text-gray-400 ml-2 text-lg blinking-underline">——顾炎武</span></p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.6s">
                                <i class="fas fa-quote-left text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <p class="highlight-quote">“我以我血荐轩辕”<span class="text-gray-400 ml-2 text-lg blinking-underline">——鲁迅</span></p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.7s">
                                <i class="fas fa-quote-left text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <p class="highlight-quote">“为什么我的眼里常含泪水？因为我对这土地爱得深沉”<span class="text-gray-400 ml-2 text-lg blinking-underline">——艾青</span></p>
                            </div>
                            <div class="flex items-start gap-4 animate-fade" style="animation-delay: 0.8s">
                                <i class="fas fa-quote-left text-[#1e90ff] mt-1.5 flex-shrink-0"></i>
                                <p class="highlight-quote">“清澈的爱，只为中国”<span class="text-gray-400 ml-2 text-lg blinking-underline">——陈祥榕</span></p>
                            </div>
                        </div>
                    </main>
                </div>
                <div class="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-[#2ed573] via-[#1e90ff] to-[#ff4757] z-20"></div>
            </div>
        </div>

        <!-- 第14页：结语 -->
        <div class="slide" id="slide14">
            <div class="relative flex flex-col w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#f8f9fa]">
                <div class="w-full h-1/2 bg-gray-200">
                    <img alt="长城日出，展现壮丽开阔的希望景象" class="w-full h-full object-cover object-top animate-scale-fade" src="https://s.coze.cn/image/GUO4vtAGxy8/"/>
                </div>
                <div class="w-full h-1/2 flex flex-col justify-center p-16">
                    <header class="mb-6">
                        <h1 class="text-4xl font-bold text-[#2d3436] animate-slide-up">七、结语：让家国情怀照亮前行之路</h1>
                        <div class="w-24 h-1.5 bg-[#1e90ff] mt-4 rounded-full"></div>
                    </header>
                    <main>
                        <p class="text-xl text-[#636e72] leading-relaxed animate-fade" style="animation-delay: 0.1s;">
                            家国情怀是中华民族的精神纽带，是穿越千年的文化基因，更是新时代奋斗者的精神坐标。从<span class="blinking-underline">土尔扈特东归</span>的民族大义，到<span class="blinking-underline">“两弹一星”</span>的科技报国；
                        </p>
                        <p class="text-xl text-[#636e72] leading-relaxed animate-fade" style="animation-delay: 0.2s;">
                            从戍边英雄的<span class="highlight-quote">“清澈的爱”</span>，到平凡岗位的默默奉献，家国情怀始终激励中华儿女砥砺前行。
                        </p>
                        <p class="text-xl text-[#636e72] leading-relaxed animate-fade" style="animation-delay: 0.3s;">
                            新时代新征程，让我们将个人梦想融入国家发展，以爱国主义铸魂、民族自豪感强基、社会责任感力行，共书民族复兴壮丽篇章。
                        </p>
                    </main>
                </div>
                <div class="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-[#1e90ff] via-[#2ed573] to-[#ff4757] z-20"></div>
            </div>
        </div>

        <!-- 第15页：致谢 -->
        <div class="slide" id="slide15">
            <div class="relative flex flex-col justify-center items-center w-[1280px] h-[720px] max-w-[1280px] max-h-[720px] aspect-[16/9] overflow-hidden bg-[#0d1a26]">
                <img alt="飘扬的五星红旗旁有多只白鸽飞翔，象征国家繁荣与和平发展" class="absolute inset-0 w-full h-full z-0 opacity-40 object-cover object-top animate-scale-fade" src="https://s.coze.cn/image/Cc0jNPmyM9U/"/>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[900px] h-[900px] bg-radial-gradient from-[#2ed573]/10 to-transparent rounded-full pointer-events-none z-10"></div>
                <div class="relative z-20 flex flex-col items-center justify-center text-center text-white p-12">
                    <h1 class="text-6xl font-bold tracking-wider text-shadow-custom animate-slide-up" style="background: linear-gradient(90deg, #ffffff, #ffd700); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        感谢聆听！
                    </h1>
                    <div class="w-32 h-1 bg-[#2ed573] my-8 rounded-full"></div>
                    <p class="text-2xl font-light tracking-widest text-shadow-custom animate-fade" style="animation-delay: 0.1s;">
                        <span class="animate-highlight">愿家国情怀永驻心间，激励我们奋勇向前。</span>
                    </p>
                </div>
                <div class="absolute bottom-0 left-0 w-full h-2 bg-gradient-to-r from-[#ff4757] via-[#1e90ff] to-[#2ed573] z-20"></div>
            </div>
        </div>
    </div>

    <!-- 控制栏 -->
    <div class="control-bar">
        <button class="control-btn" id="prevBtn"><i class="fas fa-chevron-left"></i></button>
        <button class="control-btn" id="playPauseBtn"><i class="fas fa-play"></i></button>
        <button class="control-btn" id="nextBtn"><i class="fas fa-chevron-right"></i></button>
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <div class="slide-info" id="slideInfo">第 1 页 / 共 15 页</div>
    </div>

    <script>
        // 全局变量
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        let currentSlide = 0;
        let isPlaying = false;
        let playInterval;
        const slideInfo = document.getElementById('slideInfo');
        const progressBar = document.getElementById('progressBar');
        const progressContainer = document.getElementById('progressContainer');

        // 初始化图表
        function initCharts() {
            // 教育成就图表
            const achievementCtx = document.getElementById('achievementChart');
            if (achievementCtx) {
                new Chart(achievementCtx, {
                    type: 'bar',
                    data: {
                        labels: ['学前教育', '义务教育', '高中阶段'],
                        datasets: [
                            {
                                label: '1949年',
                                data: [0.4, 20, 1.1],
                                backgroundColor: 'rgba(255, 99, 132, 0.6)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            },
                            {
                                label: '2023年',
                                data: [91.1, 100, 91.8],
                                backgroundColor: 'rgba(54, 162, 235, 0.6)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'top', labels: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } },
                            title: { display: true, text: '中国教育普及水平变化（1949-2023）', font: { family: "'Noto Sans SC', sans-serif", size: 18, weight: 'bold' }, color: '#2d3436', padding: { bottom: 20 } }
                        },
                        scales: {
                            y: { beginAtZero: true, title: { display: true, text: '毛入学率 (%)', font: { family: "'Noto Sans SC', sans-serif", size: 14 } }, ticks: { font: { family: "'Noto Sans SC', sans-serif" } } },
                            x: { ticks: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } }
                        }
                    }
                });
            }

            // 科技成就图表
            const techCtx = document.getElementById('techAchievementChart');
            if (techCtx) {
                new Chart(techCtx, {
                    type: 'line',
                    data: {
                        labels: ['2015', '2017', '2019', '2021', '2023'],
                        datasets: [
                            {
                                label: '中国研发经费投入（万亿元）',
                                data: [1.4, 1.7, 2.2, 2.8, 3.5],
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                fill: true
                            },
                            {
                                label: '全球创新指数排名',
                                data: [29, 27, 14, 12, 10],
                                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                fill: true
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'top', labels: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } },
                            title: { display: true, text: '中国科技发展成就（2015-2023）', font: { family: "'Noto Sans SC', sans-serif", size: 18, weight: 'bold' }, color: '#2d3436', padding: { bottom: 20 } }
                        },
                        scales: {
                            y: { beginAtZero: true, title: { display: true, text: ['研发经费（万亿元）', '创新指数排名'], font: { family: "'Noto Sans SC', sans-serif", size: 14 } }, ticks: { font: { family: "'Noto Sans SC', sans-serif" } } },
                            x: { ticks: { font: { family: "'Noto Sans SC', sans-serif", size: 14 } } }
                        }
                    }
                });
            }
        }

        // 显示指定幻灯片
        function showSlide(index) {
            // 隐藏所有幻灯片
            slides.forEach(slide => {
                slide.classList.remove('active');
            });
            // 显示当前幻灯片
            currentSlide = index;
            slides[currentSlide].classList.add('active');
            // 更新信息和进度
            updateSlideInfo();
            updateProgress();
            // 重新初始化当前页图表（避免图表渲染问题）
            if (currentSlide === 5 || currentSlide === 10) {
                setTimeout(initCharts, 300);
            }
        }

        // 更新幻灯片信息
        function updateSlideInfo() {
            slideInfo.textContent = `第 ${currentSlide + 1} 页 / 共 ${totalSlides} 页`;
        }

        // 更新进度条
        function updateProgress() {
            const progress = (currentSlide / (totalSlides - 1)) * 100;
            progressBar.style.width = `${progress}%`;
        }

        // 上一页
        function prevSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(currentSlide);
        }

        // 下一页
        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        }

        // 播放/暂停
        function togglePlayPause() {
            const playPauseBtn = document.getElementById('playPauseBtn');
            if (isPlaying) {
                // 暂停
                clearInterval(playInterval);
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            } else {
                // 播放
                playInterval = setInterval(nextSlide, 5000); // 5秒切换一页
                playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            }
            isPlaying = !isPlaying;
        }

        // 点击进度条跳转
        progressContainer.addEventListener('click', (e) => {
            const rect = progressContainer.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const percent = x / rect.width;
            currentSlide = Math.floor(percent * (totalSlides - 1));
            showSlide(currentSlide);
        });

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            switch (e.key) {
                case 'ArrowLeft':
                    prevSlide();
                    break;
                case 'ArrowRight':
                    nextSlide();
                    break;
                case ' ':
                    e.preventDefault();
                    togglePlayPause();
                    break;
            }
        });

        // 触摸控制（左右滑动）
        let touchStartX = 0;
        document.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
        }, false);

        document.addEventListener('touchend', (e) => {
            if (!e.changedTouches[0]) return;
            const touchEndX = e.changedTouches[0].clientX;
            const diff = touchStartX - touchEndX;
            if (diff > 50) {
                nextSlide();
            } else if (diff < -50) {
                prevSlide();
            }
        }, false);

        // 按钮事件绑定
        document.getElementById('prevBtn').addEventListener('click', prevSlide);
        document.getElementById('nextBtn').addEventListener('click', nextSlide);
        document.getElementById('playPauseBtn').addEventListener('click', togglePlayPause);

        // 初始化
        window.addEventListener('load', () => {
            showSlide(0);
            initCharts();
        });
    </script>
</body>
</html>