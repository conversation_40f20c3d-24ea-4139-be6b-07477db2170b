<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>值班表排班系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
            background-color: #f5f7fa;
        }
        
        .calendar-day {
            aspect-ratio: 1 / 1;
            min-height: 80px;
            transition: all 0.2s ease;
        }
        
        .calendar-day:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .group-1 {
            background-color: rgba(59, 130, 246, 0.1);
            border-left: 3px solid #3b82f6;
        }
        
        .group-2 {
            background-color: rgba(16, 185, 129, 0.1);
            border-left: 3px solid #10b981;
        }
        
        .weekend {
            background-color: rgba(243, 244, 246, 0.7);
        }
        
        .month-selector {
            border-radius: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .header-gradient {
            background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
        }
        
        .card {
            transition: all 0.3s ease;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        
        .group-card {
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        
        .group-card:hover {
            transform: translateY(-3px);
        }
        
        .day-name {
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        @media (max-width: 640px) {
            .calendar-day {
                min-height: 60px;
            }
            
            .day-members {
                font-size: 0.65rem;
                line-height: 1.2;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        
        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-6 max-w-6xl">
        <!-- 头部 -->
        <div class="header-gradient text-white p-6 rounded-lg shadow-lg mb-6 card">
            <h1 class="text-3xl font-bold text-center">值班表排班系统</h1>
            <p class="text-center mt-2 opacity-90">自动生成工作日排班，周末不值班</p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- 小组信息卡片 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow p-5 card">
                    <h2 class="text-xl font-bold text-gray-800 mb-4 border-b pb-2">值班小组</h2>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <!-- 第一组 -->
                        <div class="group-card bg-blue-50 p-4 relative">
                            <div class="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-medium rounded-bl-lg">小组一</div>
                            <h3 class="text-blue-600 font-semibold mt-6 mb-2">成员列表</h3>
                            <ul class="space-y-1 text-gray-700">
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    李航
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    董维雅
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    陆宇瑶
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    王梓桐
                                </li>
                            </ul>
                        </div>
                        
                        <!-- 第二组 -->
                        <div class="group-card bg-green-50 p-4 relative">
                            <div class="absolute top-0 right-0 bg-green-500 text-white px-3 py-1 text-sm font-medium rounded-bl-lg">小组二</div>
                            <h3 class="text-green-600 font-semibold mt-6 mb-2">成员列表</h3>
                            <ul class="space-y-1 text-gray-700">
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    张雅珊
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    李梓萌
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    焦羽翰
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    刘辰嘉
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 排班规则和起始日期 -->
            <div class="bg-white rounded-lg shadow p-5 card">
                <h2 class="text-xl font-bold text-gray-800 mb-4 border-b pb-2">排班设置</h2>
                
                <!-- 规则说明 -->
                <div class="bg-yellow-50 p-4 rounded-lg mb-4">
                    <h3 class="font-semibold text-yellow-700 flex items-center mb-2">
                        <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                        值班规则
                    </h3>
                    <ul class="text-sm text-gray-600 space-y-1 pl-2">
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-yellow-500 mt-0.5 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            周一至周五值班，周末不值班
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-yellow-500 mt-0.5 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            两组轮流值班，每组值班一天
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-yellow-500 mt-0.5 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            如周五是小组一，下周一为小组二
                        </li>
                    </ul>
                </div>
                
                <!-- 起始日期 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">起始日期</label>
                    <input type="date" id="start-date" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <p class="mt-1 text-xs text-gray-500">从这一天开始计算排班</p>
                </div>
                
                <button id="apply-date" class="mt-4 w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                    应用设置
                </button>
            </div>
        </div>
        
        <!-- 日历部分 -->
        <div class="bg-white rounded-lg shadow-lg p-5 card">
            <!-- 月份选择器 -->
            <div class="flex justify-between items-center month-selector bg-gray-50 p-3 mb-6">
                <button id="prev-month" class="text-indigo-600 hover:text-indigo-800 focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                
                <h2 id="current-month-year" class="text-xl font-bold text-gray-800">2025年4月</h2>
                
                <button id="next-month" class="text-indigo-600 hover:text-indigo-800 focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
            
            <!-- 日历头部 -->
            <div class="grid grid-cols-7 gap-2 mb-2">
                <div class="text-center day-name text-gray-500">周日</div>
                <div class="text-center day-name text-gray-800">周一</div>
                <div class="text-center day-name text-gray-800">周二</div>
                <div class="text-center day-name text-gray-800">周三</div>
                <div class="text-center day-name text-gray-800">周四</div>
                <div class="text-center day-name text-gray-800">周五</div>
                <div class="text-center day-name text-gray-500">周六</div>
            </div>
            
            <!-- 日历内容 -->
            <div id="calendar-grid" class="grid grid-cols-7 gap-2"></div>
            
            <!-- 图例 -->
            <div class="mt-6 flex flex-wrap justify-center gap-4">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-blue-100 border-l-2 border-blue-500 mr-2"></div>
                    <span class="text-sm text-gray-600">小组一值班</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-green-100 border-l-2 border-green-500 mr-2"></div>
                    <span class="text-sm text-gray-600">小组二值班</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-gray-100 mr-2"></div>
                    <span class="text-sm text-gray-600">周末休息</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-gray-50 mr-2"></div>
                    <span class="text-sm text-gray-600">起始日期前未安排</span>
                </div>
            </div>
        </div>
        
        <!-- 页脚 -->
        <div class="mt-6 text-center text-gray-500 text-sm">
            <p>© 2025 值班表排班系统 | 自动生成工作日排班</p>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 定义团队成员
            const members = {
                group1: ["李航", "董维雅", "陆宇瑶", "王梓桐"],
                group2: ["张雅珊", "李梓萌", "焦羽翰", "刘辰嘉"]
            };
            
            // 设置默认日期为今天
            const today = new Date();
            document.getElementById('start-date').valueAsDate = today;
            
            let currentMonth = today.getMonth();
            let currentYear = today.getFullYear();
            let startDate = today;
            
            // 判断一个日期是否为周末
            function isWeekend(date) {
                const day = date.getDay();
                return day === 0 || day === 6; // 0是周日，6是周六
            }
            
            // 获取值班组 (根据日期确定)
            function getDutyGroup(date) {
                if (isWeekend(date)) return null;
                
                // 设置日期为0点0分0秒，避免时间比较问题
                const targetDate = new Date(date);
                targetDate.setHours(0, 0, 0, 0);
                const compareStartDate = new Date(startDate);
                compareStartDate.setHours(0, 0, 0, 0);
                
                // 如果日期在起始日期之前，返回null（不显示值班）
                if (targetDate < compareStartDate) {
                    return null;
                }
                
                // 计算从开始日期到当前日期的工作日数量
                let workDays = 0;
                const tempDate = new Date(startDate);
                tempDate.setHours(0, 0, 0, 0);
                
                while (tempDate <= targetDate) {
                    if (!isWeekend(tempDate)) {
                        workDays++;
                    }
                    tempDate.setDate(tempDate.getDate() + 1);
                }
                
                // 从起始日期算起，第一个工作日是组1
                // 根据工作日奇偶性确定值班组
                return workDays % 2 === 1 ? 1 : 2;
            }
            
            // 更新日历
            function updateCalendar() {
                const calendarGrid = document.getElementById('calendar-grid');
                calendarGrid.innerHTML = '';
                
                // 更新月份标题
                const monthNames = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"];
                document.getElementById('current-month-year').innerText = `${currentYear}年${monthNames[currentMonth]}`;
                
                // 获取本月第一天和最后一天
                const firstDay = new Date(currentYear, currentMonth, 1);
                const lastDay = new Date(currentYear, currentMonth + 1, 0);
                
                // 获取本月第一天是星期几（0-6）
                const firstDayOfWeek = firstDay.getDay();
                
                // 添加上月剩余天数的空白格
                for (let i = 0; i < firstDayOfWeek; i++) {
                    const emptyDay = document.createElement('div');
                    emptyDay.className = 'calendar-day bg-gray-50 p-2 rounded-lg';
                    calendarGrid.appendChild(emptyDay);
                }
                
                // 添加本月的天数
                for (let day = 1; day <= lastDay.getDate(); day++) {
                    const date = new Date(currentYear, currentMonth, day);
                    const dayElement = document.createElement('div');
                    
                    // 判断是否为周末
                    const isWeekendDay = isWeekend(date);
                    
                    // 获取值班组
                    const dutyGroup = getDutyGroup(date);
                    
                    // 设置日期样式
                    if (isWeekendDay) {
                        dayElement.className = 'calendar-day weekend p-2 rounded-lg';
                    } else if (dutyGroup === 1) {
                        dayElement.className = 'calendar-day group-1 p-2 rounded-lg';
                    } else if (dutyGroup === 2) {
                        dayElement.className = 'calendar-day group-2 p-2 rounded-lg';
                    } else {
                        // 起始日期之前的工作日
                        dayElement.className = 'calendar-day bg-gray-50 p-2 rounded-lg';
                    }
                    
                    // 添加日期数字
                    const dayNumber = document.createElement('div');
                    dayNumber.className = 'text-right font-medium text-gray-700';
                    dayNumber.innerText = day;
                    dayElement.appendChild(dayNumber);
                    
                    // 添加值班信息
                    if (isWeekendDay) {
                        const weekendInfo = document.createElement('div');
                        weekendInfo.className = 'flex items-center justify-center h-3/4';
                        weekendInfo.innerHTML = '<span class="text-gray-400 text-sm">休息日</span>';
                        dayElement.appendChild(weekendInfo);
                    } else if (dutyGroup) {
                        const groupInfo = document.createElement('div');
                        groupInfo.className = 'mt-1';
                        
                        const groupTitle = document.createElement('div');
                        groupTitle.className = dutyGroup === 1 ? 
                            'text-center font-medium text-blue-600 text-sm' : 
                            'text-center font-medium text-green-600 text-sm';
                        groupTitle.innerText = `小组${dutyGroup}`;
                        
                        const groupMembers = document.createElement('div');
                        groupMembers.className = 'text-xs mt-1 text-center text-gray-600 day-members';
                        groupMembers.innerText = dutyGroup === 1 ? 
                            members.group1.join('、') : 
                            members.group2.join('、');
                        
                        groupInfo.appendChild(groupTitle);
                        groupInfo.appendChild(groupMembers);
                        dayElement.appendChild(groupInfo);
                    } else {
                        // 当不是周末但也没有值班组时（起始日期之前的工作日）
                        const emptyInfo = document.createElement('div');
                        emptyInfo.className = 'flex items-center justify-center h-3/4';
                        emptyInfo.innerHTML = '<span class="text-gray-400 text-sm">未安排</span>';
                        dayElement.appendChild(emptyInfo);
                    }
                    
                    calendarGrid.appendChild(dayElement);
                }
            }
            
            // 切换到上个月
            document.getElementById('prev-month').addEventListener('click', function() {
                if (currentMonth === 0) {
                    currentMonth = 11;
                    currentYear--;
                } else {
                    currentMonth--;
                }
                updateCalendar();
            });
            
            // 切换到下个月
            document.getElementById('next-month').addEventListener('click', function() {
                if (currentMonth === 11) {
                    currentMonth = 0;
                    currentYear++;
                } else {
                    currentMonth++;
                }
                updateCalendar();
            });
            
            // 应用起始日期
            document.getElementById('apply-date').addEventListener('click', function() {
                const dateInput = document.getElementById('start-date');
                if (dateInput.value) {
                    startDate = new Date(dateInput.value);
                    updateCalendar();
                }
            });
            
            // 初始化日历
            updateCalendar();
        });
    </script>
</body>
</html>