<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=1280, initial-scale=1.0" name="viewport"/>
    <title>家国情怀名言警句集锦</title>
    <script src="https://unpkg.byted-static.com/coze/space_ppt_lib/1.0.3-alpha.12/lib/tailwindcss.js"></script>
    <script src="https://unpkg.byted-static.com/fortawesome/fontawesome-free/6.7.2/js/all.min.js" data-auto-replace-svg="nest"></script>
    <link href="https://lf-code-agent.coze.cn/obj/x-ai-cn/fonts/google/google-all-fonts.css" rel="stylesheet"/>
    <style>
        .font-noto { font-family: 'Noto Sans SC', sans-serif; }
        
        /* 基础样式 */
        body { 
            margin: 0; 
            padding: 0; 
            background: #0d1a26;
            overflow: hidden;
        }
        
        .slide-container {
            width: 1280px;
            height: 720px;
            position: relative;
            overflow: hidden;
        }
        
        /* 背景样式 */
        .bg-image {
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0.5;
            z-index: 0;
        }
        
        .bg-gradient {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 900px;
            height: 900px;
            background: radial-gradient(circle, rgba(30, 144, 255, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1;
        }
        
        /* 标题样式 */
        .slide-header {
            position: relative;
            z-index: 2;
            text-align: center;
            padding: 40px 0 20px;
        }
        
        .slide-title {
            font-size: 5xl;
            font-weight: bold;
            background: linear-gradient(90deg, #ffffff, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.6);
            margin: 0;
        }
        
        .title-underline {
            width: 120px;
            height: 3px;
            background: #ff4757;
            margin: 15px auto 0;
            border-radius: 2px;
        }
        
        /* 名言容器 */
        .quotes-container {
            position: relative;
            z-index: 2;
            width: 90%;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
        }
        
        /* 名言卡片样式 */
        .quote-card {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .quote-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.05);
            transform: scale(0);
            border-radius: 8px;
            transition: transform 0.3s ease;
            z-index: -1;
        }
        
        .quote-card:hover::before {
            transform: scale(1);
        }
        
        .quote-icon {
            color: #1e90ff;
            font-size: 24px;
            margin-top: 3px;
            flex-shrink: 0;
        }
        
        .quote-content {
            flex-grow: 1;
        }
        
        .quote-text {
            color: #ffffff;
            font-size: 20px;
            line-height: 1.6;
            margin: 0 0 5px 0;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
            transition: all 0.3s ease;
        }
        
        .quote-author {
            color: #d0d0d0;
            font-size: 16px;
            margin: 0;
            transition: all 0.3s ease;
        }
        
        /* 悬停效果 */
        .quote-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
        }
        
        .quote-card:hover .quote-text {
            color: #ffd700;
        }
        
        .quote-card:hover .quote-author {
            color: #ffffff;
        }
        
        .quote-card:hover .quote-icon {
            color: #ffd700;
            transform: scale(1.2);
        }
        
        /* 播放状态指示器 */
        .playing-indicator {
            position: absolute;
            right: 15px;
            bottom: 15px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #2ed573;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .quote-card.playing .playing-indicator {
            opacity: 1;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(0.8); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(0.8); opacity: 1; }
        }
        
        /* 底部装饰条 */
        .bottom-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(to right, #2ed573, #1e90ff, #ff4757);
            z-index: 3;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
            opacity: 0;
        }
        
        .delay-100 { animation-delay: 0.1s; }
        .delay-200 { animation-delay: 0.2s; }
        .delay-300 { animation-delay: 0.3s; }
        .delay-400 { animation-delay: 0.4s; }
        .delay-500 { animation-delay: 0.5s; }
        .delay-600 { animation-delay: 0.6s; }
        .delay-700 { animation-delay: 0.7s; }
        .delay-800 { animation-delay: 0.8s; }
    </style>
</head>
<body class="font-noto">
    <div class="slide-container">
        <!-- 背景图片 -->
        <img src="https://space.coze.cn/s/X2E_p5psnkY/" alt="水墨风格卷轴背景，营造典雅庄重氛围" class="bg-image">
        
        <!-- 背景渐变 -->
        <div class="bg-gradient"></div>
        
        <!-- 标题区域 -->
        <header class="slide-header animate-fade-in">
            <h1 class="slide-title">家国情怀名言警句集锦</h1>
            <div class="title-underline"></div>
        </header>
        
        <!-- 名言内容区域 -->
        <main class="quotes-container">
            <!-- 名言1 -->
            <div class="quote-card animate-fade-in delay-100" data-audio="https://example.com/audio/gouli.mp3">
                <i class="fas fa-quote-left quote-icon"></i>
                <div class="quote-content">
                    <p class="quote-text">苟利国家，不求富贵</p>
                    <p class="quote-author">——《礼记》</p>
                </div>
                <div class="playing-indicator"></div>
            </div>
            
            <!-- 名言2 -->
            <div class="quote-card animate-fade-in delay-200" data-audio="https://example.com/audio/weigan.mp3">
                <i class="fas fa-quote-left quote-icon"></i>
                <div class="quote-content">
                    <p class="quote-text">位卑未敢忘忧国</p>
                    <p class="quote-author">——陆游</p>
                </div>
                <div class="playing-indicator"></div>
            </div>
            
            <!-- 名言3 -->
            <div class="quote-card animate-fade-in delay-300" data-audio="https://example.com/audio/danxin.mp3">
                <i class="fas fa-quote-left quote-icon"></i>
                <div class="quote-content">
                    <p class="quote-text">人生自古谁无死，留取丹心照汗青</p>
                    <p class="quote-author">——文天祥</p>
                </div>
                <div class="playing-indicator"></div>
            </div>
            
            <!-- 名言4 -->
            <div class="quote-card animate-fade-in delay-400" data-audio="https://example.com/audio/xianxia.mp3">
                <i class="fas fa-quote-left quote-icon"></i>
                <div class="quote-content">
                    <p class="quote-text">先天下之忧而忧，后天下之乐而乐</p>
                    <p class="quote-author">——范仲淹</p>
                </div>
                <div class="playing-indicator"></div>
            </div>
            
            <!-- 名言5 -->
            <div class="quote-card animate-fade-in delay-500" data-audio="https://example.com/audio/pifu.mp3">
                <i class="fas fa-quote-left quote-icon"></i>
                <div class="quote-content">
                    <p class="quote-text">天下兴亡，匹夫有责</p>
                    <p class="quote-author">——顾炎武</p>
                </div>
                <div class="playing-indicator"></div>
            </div>
            
            <!-- 名言6 -->
            <div class="quote-card animate-fade-in delay-600" data-audio="https://example.com/audio/xianyuan.mp3">
                <i class="fas fa-quote-left quote-icon"></i>
                <div class="quote-content">
                    <p class="quote-text">我以我血荐轩辕</p>
                    <p class="quote-author">——鲁迅</p>
                </div>
                <div class="playing-indicator"></div>
            </div>
            
            <!-- 名言7 -->
            <div class="quote-card animate-fade-in delay-700" data-audio="https://example.com/audio/landai.mp3">
                <i class="fas fa-quote-left quote-icon"></i>
                <div class="quote-content">
                    <p class="quote-text">为什么我的眼里常含泪水？因为我对这土地爱得深沉</p>
                    <p class="quote-author">——艾青</p>
                </div>
                <div class="playing-indicator"></div>
            </div>
            
            <!-- 名言8 -->
            <div class="quote-card animate-fade-in delay-800" data-audio="https://example.com/audio/qingche.mp3">
                <i class="fas fa-quote-left quote-icon"></i>
                <div class="quote-content">
                    <p class="quote-text">清澈的爱，只为中国</p>
                    <p class="quote-author">——陈祥榕</p>
                </div>
                <div class="playing-indicator"></div>
            </div>
        </main>
        
        <!-- 底部装饰条 -->
        <div class="bottom-bar"></div>
    </div>

    <script>
        // 创建音频上下文用于音效处理
        let audioContext;
        let currentAudio = null;
        
        // 获取所有名言卡片
        const quoteCards = document.querySelectorAll('.quote-card');
        
        // 为每个卡片添加点击事件
        quoteCards.forEach(card => {
            card.addEventListener('click', async () => {
                // 停止当前正在播放的音频
                if (currentAudio) {
                    currentAudio.pause();
                    document.querySelector('.quote-card.playing')?.classList.remove('playing');
                }
                
                // 获取音频URL
                const audioUrl = card.dataset.audio;
                
                try {
                    // 初始化音频上下文
                    if (!audioContext) {
                        audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    }
                    
                    // 加载并播放音频
                    const response = await fetch(audioUrl);
                    const arrayBuffer = await response.arrayBuffer();
                    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
                    
                    // 创建音频源和增益节点
                    const source = audioContext.createBufferSource();
                    const gainNode = audioContext.createGain();
                    
                    source.buffer = audioBuffer;
                    source.connect(gainNode);
                    gainNode.connect(audioContext.destination);
                    
                    // 播放音频
                    source.start(0);
                    currentAudio = source;
                    
                    // 添加播放状态
                    card.classList.add('playing');
                    
                    // 音频结束时移除播放状态
                    source.onended = () => {
                        card.classList.remove('playing');
                        currentAudio = null;
                    };
                } catch (error) {
                    console.error('音频播放失败:', error);
                    // 显示错误提示（实际使用时可替换为更友好的UI提示）
                    alert('音频加载失败，请检查网络连接或稍后再试');
                }
            });
        });
        
        // 页面加载完成后初始化动画
        window.addEventListener('load', () => {
            // 确保动画正常触发
            document.querySelectorAll('.animate-fade-in').forEach(el => {
                el.style.animationPlayState = 'running';
            });
        });
    </script>
</body>
</html>
